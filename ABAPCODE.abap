SELECT DISTINCT a~partner,
mc_name1 AS bpname,
a~bu_group
FROM but000 AS a
INNER JOIN zmmt_bp_open AS b ON b~partner = a~partner
WHERE a~mc_name1 IN @s_name1
AND a~bu_group IN ('Z001','Z002')
INTO TABLE @lt_bp.
IF sy-subrc <> 0.
MESSAGE '查询不到BP信息' TYPE 'S' DISPLAY LIKE 'E'.
LEAVE LIST-PROCESSING.
ENDIF.

*   当分组取值为Z002时，根据业务伙伴从表LFA1取出对应客户编号KUNNR；
*   当分组取值为Z001时，根据业务伙伴从表KNA1取出对应供应商编号LIFNR；
LOOP AT lt_bp ASSIGNING FIELD-SYMBOL(<lfs_bp>) WHERE partner IS NOT INITIAL.
**      IF <lfs_bp>-bu_group = 'Z001'.
<lfs_bp>-kunnr = <lfs_bp>-partner.
SELECT SINGLE lifnr FROM kna1
INTO <lfs_bp>-lifnr
WHERE kunnr = <lfs_bp>-partner.
**      ELSEIF <lfs_bp>-bu_group = 'Z002'.
**        <lfs_bp>-lifnr = <lfs_bp>-partner.
SELECT SINGLE kunnr FROM lfa1
INTO <lfs_bp>-kunnr
WHERE lifnr = <lfs_bp>-partner.
**      ENDIF.
ENDLOOP.
