Object type VKOI not supported, 2 objects ignored 

Object type VKOS not supported, 2 objects ignored 

 语言 2Q 不在系统中 

 PROG ZMMD018_9000 already exists outside of ZQH1 package hierarchy 

表 ZFIT_BANKACC 不是数据库表 ＝> 不适合作为选择方法 

缺少用于表的增强类别 

缺少用于包含文件或子类型的增强类别 

字段 ZZHTBH：所用组件类型或域为非活动或不存在 

字段 ZZHTMC：所用组件类型或域为非活动或不存在 

无法确定 CI_EKKODBX 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

 数据类型 不存在 

无法生成表 CI_EKKODBX 的名称表 

缺少用于表的增强类别 

缺少用于包含文件或子类型的增强类别 

缺少用于表的增强类别 

缺少用于包含文件或子类型的增强类别 

字段 ZZGXDQ：所用组件类型或域为非活动或不存在 

无法确定 ZABAPQH_VBAP 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

 数据类型 不存在 

无法生成表 ZABAPQH_VBAP 的名称表 

缺少用于表的增强类别 

缺少用于包含文件或子类型的增强类别 

保留字段名 ROW (不要使用数据库表中 INCLUDE 这样的结构) 

 字段 COL：所用组件类型或域为非活动或不存在 

字段 ROW：所用组件类型或域为非活动或不存在 

无法确定 ZALSMEX_TABLINE3 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

Execution of the consistency checks failed. 

 数据类型 不存在 

无法生成表 ZALSMEX_TABLINE3 的名称表 

字段 ZZGXDQ：所用组件类型或域为非活动或不存在 

无法确定 ZBVBAPKOZ 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

 数据类型 不存在 

无法生成表 ZBVBAPKOZ 的名称表 

缺少用于表的增强类别 

缺少用于包含文件或子类型的增强类别 

缺少用于表的增强类别 

缺少用于包含文件或子类型的增强类别 

缺少用于表的增强类别 

缺少用于包含文件或子类型的增强类别 

字段 AUFNR_XB：所用组件类型或域为非活动或不存在 

字段 BUS_CLASS_TXT：所用组件类型或域为非活动或不存在 

字段 CONTAXT：所用组件类型或域为非活动或不存在 

字段 CON_NO：所用组件类型或域为非活动或不存在 

字段 DEBIT_MANE：所用组件类型或域为非活动或不存在 

字段 DEBT_TARGET_TXT：所用组件类型或域为非活动或不存在 

字段 KTEXT_XB：所用组件类型或域为非活动或不存在 

字段 STOCK_TYPE_TXT：所用组件类型或域为非活动或不存在 

字段 TARGET_TXT：所用组件类型或域为非活动或不存在 

字段 ZDYHLX：所用组件类型或域为非活动或不存在 

字段 ZDYHMS：所用组件类型或域为非活动或不存在 

字段 ZFQMC：所用组件类型或域为非活动或不存在 

字段 ZFQMC1：所用组件类型或域为非活动或不存在 

字段 ZFQZT：所用组件类型或域为非活动或不存在 

字段 ZKMFZMS：所用组件类型或域为非活动或不存在 

字段 ZXMBC：所用组件类型或域为非活动或不存在 

字段 ZXMBC1：所用组件类型或域为非活动或不存在 

字段 ZXMBM：所用组件类型或域为非活动或不存在 

字段 ZXMBM1：所用组件类型或域为非活动或不存在 

字段 ZXMLX：所用组件类型或域为非活动或不存在 

字段 ZZ008_TXT：所用组件类型或域为非活动或不存在 

字段 ZZ013_TXT：所用组件类型或域为非活动或不存在 

字段 ZZ014_TXT：所用组件类型或域为非活动或不存在 

字段 ZZ016_TXT：所用组件类型或域为非活动或不存在 

字段 ZZBUS_CLASS：所用组件类型或域为非活动或不存在 

无法确定 ZFIS_ACCOUNT_BALANCE_A02 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

 搜索帮助接口的搜索字段 ZFIS_ACCOUNT_BALANCE_A02-MWSKZ：搜索帮助 ZSH_T007A 处于非活动状态 

数据类型 不存在 

无法生成表 ZFIS_ACCOUNT_BALANCE_A02 的名称表 

缺少用于表的增强类别 

缺少用于包含文件或子类型的增强类别 

字段 AUFNR_XB：所用组件类型或域为非活动或不存在 

字段 BUS_CLASS_TXT：所用组件类型或域为非活动或不存在 

字段 CONTAXT：所用组件类型或域为非活动或不存在 

字段 CON_NO：所用组件类型或域为非活动或不存在 

字段 DEBIT_MANE：所用组件类型或域为非活动或不存在 

字段 DEBT_TARGET_TXT：所用组件类型或域为非活动或不存在 

字段 KTEXT_XB：所用组件类型或域为非活动或不存在 

字段 STOCK_TYPE_TXT：所用组件类型或域为非活动或不存在 

字段 TARGET_TXT：所用组件类型或域为非活动或不存在 

字段 ZDYHLX：所用组件类型或域为非活动或不存在 

字段 ZDYHMS：所用组件类型或域为非活动或不存在 

字段 ZFQMC：所用组件类型或域为非活动或不存在 

字段 ZFQMC1：所用组件类型或域为非活动或不存在 

字段 ZFQZT：所用组件类型或域为非活动或不存在 

字段 ZKMFZMS：所用组件类型或域为非活动或不存在 

字段 ZXMBC：所用组件类型或域为非活动或不存在 

字段 ZXMBC1：所用组件类型或域为非活动或不存在 

字段 ZXMBM：所用组件类型或域为非活动或不存在 

字段 ZXMBM1：所用组件类型或域为非活动或不存在 

字段 ZXMLX：所用组件类型或域为非活动或不存在 

字段 ZZ008_TXT：所用组件类型或域为非活动或不存在 

字段 ZZ013_TXT：所用组件类型或域为非活动或不存在 

字段 ZZ014_TXT：所用组件类型或域为非活动或不存在 

字段 ZZ016_TXT：所用组件类型或域为非活动或不存在 

字段 ZZBUS_CLASS：所用组件类型或域为非活动或不存在 

无法确定 ZFIS_ACCOUNT_BALANCE_DETAIL_A2 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

 数据类型 不存在 

无法生成表 ZFIS_ACCOUNT_BALANCE_DETAIL_A2 的名称表 

缺少用于表的增强类别 

缺少用于包含文件或子类型的增强类别 

字段 BOE_NUM：所用组件类型或域为非活动或不存在 

字段 PAYMENT_LINE：所用组件类型或域为非活动或不存在 

字段 PAY_BANK_ACCOUNT：所用组件类型或域为非活动或不存在 

无法确定 ZFIS_BOE_FEEDBACK_PROC 的结构宽度 

 ZFIS_BOE_FEEDBACK_PROC-PAYMENT_AMOUNT（组合索引表/字段 ZFIT_I010A-CURRENCY_CODE 不存在或数据类型错误） 

 Initialization of Extensibility Contract Check failed. 

 数据类型 不存在 

无法生成表 ZFIS_BOE_FEEDBACK_PROC 的名称表

搜索帮助接口的搜索字段 ZFIT_DOC_SUBST-TCODE：搜索帮助 ZSH_TCODE 处于非活动状态 

保留字段名 OPTION (不要使用数据库表中 INCLUDE 这样的结构) 

 字段 HIGH：所用组件类型或域为非活动或不存在 

字段 LOW：所用组件类型或域为非活动或不存在 

无法确定 ZMMS_SQID 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

 数据类型 不存在 

无法生成表 ZMMS_SQID 的名称表 

缺少用于表的增强类别 

缺少用于包含文件或子类型的增强类别 

键值字段 ZZGXDQ 具有无效类型 

字段 ZZGXDQ：所用组件类型或域为非活动或不存在 

无法确定 ZMMT_GXDQ_MAT 的结构宽度 

由于类型 ZZGXDQ 不存在缺省，字段 不能作为关键字段使用 

 Initialization of Extensibility Contract Check failed. 

 数据类型 不存在 

无法生成表 ZMMT_GXDQ_MAT 的名称表 



外来码 CDS_M_OA_SOS_B-PS_PSP_PNR （ PS_PSP_PNR 与 PSPNR 指向不同的域） 

标志：“不正确的增强类别”未更新 (源) 

 视图结构 CDS_M_OA_SOS_B 不能被激活

CRRBSLSPRDYNITEM-BASEUNITACTUALCOST (指定参考表和参考字段) 

 标志：“不正确的增强类别”未更新 (源) 

 视图结构 CRRBSLSPRDYNITEM 不能被激活 

 CRTNRDOCITMF1708-RETURNABLEQUANTITY (指定参考表和参考字段) 

 标志：“不正确的增强类别”未更新 (源) 

 视图结构 CRTNRDOCITMF1708 不能被激活 

CRRBSLSPRDYNITEM-BASEUNITACTUALCOST (指定参考表和参考字段) 

 标志：“不正确的增强类别”未更新 (源) 

 视图结构 CRRBSLSPRDYNITEM 不能被激活 

 CRTNRDOCITMF1708-RETURNABLEQUANTITY (指定参考表和参考字段) 

 标志：“不正确的增强类别”未更新 (源) 

 视图结构 CRTNRDOCITMF1708 不能被激活 

不能调整全局视图 IFIGRIRMONITOR（在多种形式的表 I_GRIRPROCESSBASIC 上） 

搜索帮助 MAT1_S_MPN 未继承：视图中缺少基础字段 EBAN-EMATN 

 搜索帮助 MAT1 未继承：视图中缺少基础字段 EKPO-EMATN 

 参照字段IFLOGRETSRVCPO-EXPECTEDOVERALLLIMITAMOUNT内不一致性 

参照字段IFLOGRETSRVCPO-OVERALLLIMITAMOUNT内不一致性 

搜索帮助 MAT1 未继承：视图中缺少基础字段 IMMPURGDOCITEM-MANUFACTURERMATERIAL 

 参照字段IFLPURITM-ACTUALDELIVERYQUANTITY内不一致性 

搜索帮助 MAT1 未继承：视图中缺少基础字段 IMMPURGDOCITEM-MANUFACTURERMATERIAL 

 外来码 IFLSPDF-PRECEDINGDOCUMENT （ PRECEDINGDOCUMENT 与 AUFNR 指向不同的域） 

标志：“不正确的增强类别”未更新 (源) 

 视图结构 IFLSPDF 不能被激活 

搜索帮助 MAT1 未继承：视图中缺少基础字段 EKPO-EMATN 

IJPANNEX204BOOKC-NETPRICEQUANTITY (指定参考表和参考字段) 

 标志：“不正确的增强类别”未更新 (源) 

 视图结构 IJPANNEX204BOOKC 不能被激活 

 IMATCOV-MRPAVAILABLESTOCKQTY (指定参考表和参考字段) 

 标志：“不正确的增强类别”未更新 (源) 

 视图结构 IMATCOV 不能被激活 

搜索帮助 MAT1 未继承：视图中缺少基础字段 IMMPURGDOCITEM-MANUFACTURERMATERIAL 

 外来码 IMMPURCHORDRITEM-COMMITMENTITEMSHORTID （ COMMITMENTITEMSHORTID 与 FIPOS 指向不同的域） 

外来码 IMMPURCHORDRITEM-WBSELEMENTINTERNALID （ WBSELEMENTINTERNALID 与 PSPNR 指向不同的域） 

标志：“不正确的增强类别”未更新 (源) 

 视图结构 IMMPURCHORDRITEM 不能被激活 

 IMMPURGDOCITEM-PURCHASINGDOCUMENTITEMCATEGORY 选择条件包含小写 

搜索帮助 MAT1 未继承：视图中缺少基础字段 IMMPURGDOCITEM-MANUFACTURERMATERIAL 

 外来码 IMRPOUTAGRMT-WBSELEMENTINTERNALID （ WBSELEMENTINTERNALID 与 PSPNR 指向不同的域） 

标志：“不正确的增强类别”未更新 (源) 

 视图结构 IMRPOUTAGRMT 不能被激活 

外来码 ISDSALESDOCITEM-WBSELEMENTINTERNALID （ WBSELEMENTINTERNALID 与 PSPNR 指向不同的域） 

 视图结构 ISDSALESDOCITEM 不能被激活 

参照字段ISDSCHEDGAGRMTIC-CUMULATIVERECEIPTQUANTITY内不一致性 

参照字段ISDSLSCONTITMANA-TOTALNETAMOUNT内不一致性 

外来码 ISDSLSDOCEXTITM-WBSELEMENTINTERNALID （ WBSELEMENTINTERNALID 与 PSPNR 指向不同的域） 

 视图结构 ISDSLSDOCEXTITM 不能被激活 

外来码 PCONFOVPSALESDOC-CONFIGURATIONOBJECT （ CONFIGURATIONOBJECT 与 VBELN 指向不同的域） 

 视图结构 PCONFOVPSALESDOC 不能被激活 

外来码 PCONFOWNSIM-SALESDOCUMENTTYPE （ SALESDOCUMENTTYPE 与 AUART 指向不同的域） 


 视图结构 PCONFOWNSIM 不能被激活 

外来码 PJITMATLRCPNTDOC-NEWDOCUMENTNUMBER （ NEWDOCUMENTNUMBER 与 EBELN 指向不同的域） 

 视图结构 PJITMATLRCPNTDOC 不能被激活

外来码 POCRSOI2ORD-ROOTOBJECTID （ ROOTOBJECTID 与 AUFNR 指向不同的域） 


 视图结构 POCRSOI2ORD 不能被激活 

外来码 POCRSOI2WBS-ROOTOBJECTID （ ROOTOBJECTID 与 PSPNR 指向不同的域） 

 视图结构 POCRSOI2WBS 不能被激活 

外来码 PSIMOBJUNI-CONFIGURATIONOBJECT （ CONFIGURATIONOBJECT 与 VBELN 指向不同的域） 

 视图结构 PSIMOBJUNI 不能被激活 

VBAP-MANDT 的选择条件：比较值 &$SESSION.CLIENT 缺少引号 
外来码 RMMPURGDOCITEM-COMMITMENTITEMSHORTID （ COMMITMENTITEMSHORTID 与 FIPOS 指向不同的域） 

外来码 RMMPURGDOCITEM-CONSUMPTIONTAXCTRLCODE （ CONSUMPTIONTAXCTRLCODE 与 STEUC 指向不同的域） 

外来码 RMMPURGDOCITEM-IN_GSTCONTROLCODE （ IN_GSTCONTROLCODE 与 STEUC 指向不同的域） 

外来码 RMMPURGDOCITEM-WBSELEMENTINTERNALID （ WBSELEMENTINTERNALID 与 PSPNR 指向不同的域） 

标志：“不正确的增强类别”未更新 (源) 
视图结构 RMMPURGDOCITEM 不能被激活 

外来码 V_POAC_REFKY_CFI-REF_KEY （ REF_KEY 与 EBELN 指向不同的域） 
视图结构 V_POAC_REFKY_CFI 不能被激活 

EKPO-MANDT 的选择条件：比较值 &$SESSION.CLIENT 缺少引号 

字段 ZZDATUM：所用组件类型或域为非活动或不存在 

字段 ZZGXDQ：所用组件类型或域为非活动或不存在 

字段 ZZYPC：所用组件类型或域为非活动或不存在 

无法确定 CI_EKPODB 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

 数据类型 不存在 

无法生成表 CI_EKPODB 的名称表 

搜索帮助接口的搜索字段 ZFIT_BANK_MAP-ZZ002：搜索帮助 ZSH_BANKACC 处于非活动状态 

字段 ZZKBETRC：所用组件类型或域为非活动或不存在 

无法确定 ZKOMP_APPEND 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

 数据类型 不存在 

无法生成表 ZKOMP_APPEND 的名称表 

字段 Z_RESLO：所用组件类型或域为非活动或不存在 

无法确定 ZMMS_MB51 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

 数据类型 不存在 

无法生成表 ZMMS_MB51 的名称表 

字段 RSP_BASEINFO：所用组件类型或域为非活动或不存在 

无法确定 ZQH_DT_E3050_GR_RSP_E_RESPONSE 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

Execution of the consistency checks failed. 

 数据类型 不存在 

无法生成表 ZQH_DT_E3050_GR_RSP_E_RESPONSE 的名称表 

字段 RSP_BASEINFO：所用组件类型或域为非活动或不存在 

无法确定 ZQH_DT_E3060_GI_RSP_E_RESPONSE 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

Execution of the consistency checks failed. 

 数据类型 不存在 

无法生成表 ZQH_DT_E3060_GI_RSP_E_RESPONSE 的名称表 

字段 ZZGXDQ：所用组件类型或域为非活动或不存在 

无法确定 ZSQH1_MM_001 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

 数据类型 不存在 

无法生成表 ZSQH1_MM_001 的名称表 

搜索帮助接口的搜索字段 ZFIT_R105_HCONF-FZZ016：搜索帮助 ZSH_ZZ016 处于非活动状态 

搜索帮助接口的搜索字段 ZFIT_R105_HCONF-FZZ019：搜索帮助 ZSH_ZZ019 处于非活动状态 

搜索帮助接口的搜索字段 ZFIT_R105_HCONF-TZZ016：搜索帮助 ZSH_ZZ016 处于非活动状态 

搜索帮助接口的搜索字段 ZFIT_R105_HCONF-TZZ019：搜索帮助 ZSH_ZZ019 处于非活动状态 

字段级的结构更改 (转换表 ZMMT_HTYS_ITEM) 

 字段级的结构更改 (转换表 ZMMT_JYYS_HEAD) 

字段 CLASS_NAME：所用组件类型或域为非活动或不存在 

字段 K2ID：所用组件类型或域为非活动或不存在 

字段 ZUSER1：所用组件类型或域为非活动或不存在 

字段 ZUSER2：所用组件类型或域为非活动或不存在 

无法确定 ZMMT_SETTLE_HEAD 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

 数据类型 不存在 

无法生成表 ZMMT_SETTLE_HEAD 的名称表 

组件 ZQH_DT_E3050_GR_RSP_E_RESPONSE 的类型 E_RESPONSE 不活动 

例程 EXPAND (无法扩展表 ZQH_DT_E3050_GR_RSP) 

 组件 ZQH_DT_E3060_GI_RSP_E_RESPONSE 的类型 E_RESPONSE 不活动 

例程 EXPAND (无法扩展表 ZQH_DT_E3060_GI_RSP) 

字段 REQ_BASEINFO：所用组件类型或域为非活动或不存在 

无法确定 ZFI_DT_INNER_ORDER_REQ_I_REQUE 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

Execution of the consistency checks failed. 

 数据类型 不存在 

无法生成表 ZFI_DT_INNER_ORDER_REQ_I_REQUE 的名称表 

字段 RSP_BASEINFO：所用组件类型或域为非活动或不存在 

无法确定 ZFI_DT_INNER_ORDER_RSP_E_RESPO 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

Execution of the consistency checks failed. 

 数据类型 不存在 

无法生成表 ZFI_DT_INNER_ORDER_RSP_E_RESPO 的名称表 

字段 ZZGXDQ：所用组件类型或域为非活动或不存在 

无法确定 ZMMT_SETTLE_ITEM 的结构宽度 

 ZMMT_SETTLE_ITEM-NET_AMOUNT（组合索引表/字段 ZMMT_SETTLE_HEAD-WAERS 不存在或数据类型错误） 

 ZMMT_SETTLE_ITEM-ORD_PRICE（组合索引表/字段 ZMMT_SETTLE_HEAD-WAERS 不存在或数据类型错误） 

 ZMMT_SETTLE_ITEM-OTH_PRICE（组合索引表/字段 ZMMT_SETTLE_HEAD-WAERS 不存在或数据类型错误） 

 ZMMT_SETTLE_ITEM-SETL_PRICE（组合索引表/字段 ZMMT_SETTLE_HEAD-WAERS 不存在或数据类型错误） 

 ZMMT_SETTLE_ITEM-TAX_AMOUNT（组合索引表/字段 ZMMT_SETTLE_HEAD-WAERS 不存在或数据类型错误） 

 ZMMT_SETTLE_ITEM-TOTAL_AMOUNT（组合索引表/字段 ZMMT_SETTLE_HEAD-WAERS 不存在或数据类型错误） 

 Initialization of Extensibility Contract Check failed. 

 数据类型 不存在 

无法生成表 ZMMT_SETTLE_ITEM 的名称表 

字段 REQ_BASEINFO：所用组件类型或域为非活动或不存在 

无法确定 ZQH_DT_E3050_GR_REQ_I_REQUEST 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

Execution of the consistency checks failed. 

 数据类型 不存在 

无法生成表 ZQH_DT_E3050_GR_REQ_I_REQUEST 的名称表 

字段 REQ_BASEINFO：所用组件类型或域为非活动或不存在 

无法确定 ZQH_DT_E3060_GI_REQ_I_REQUEST 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

Execution of the consistency checks failed. 

 数据类型 不存在 

无法生成表 ZQH_DT_E3060_GI_REQ_I_REQUEST 的名称表 

字段 REQ_BASEINFO：所用组件类型或域为非活动或不存在 

无法确定 ZQH_DT_E3070_GR_CONTRACT_REQ_I 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

Execution of the consistency checks failed. 

 数据类型 不存在 

无法生成表 ZQH_DT_E3070_GR_CONTRACT_REQ_I 的名称表 

字段 RSP_BASEINFO：所用组件类型或域为非活动或不存在 

无法确定 ZQH_DT_E3070_GR_CONTRACT_RSP_E 的结构宽度 

 Initialization of Extensibility Contract Check failed. 

Execution of the consistency checks failed. 

 数据类型 不存在 

无法生成表 ZQH_DT_E3070_GR_CONTRACT_RSP_E 的名称表 

组件 ZQH_DT_E3050_GR_RSP 的类型 MT_E3050_GR_RSP 不活动 

例程 EXPAND (无法扩展表 ZQH_MT_E3050_GR_RSP) 

 组件 ZQH_DT_E3060_GI_RSP 的类型 MT_E3060_GI_RSP 不活动 

例程 EXPAND (无法扩展表 ZQH_MT_E3060_GI_RSP) 

组件 ZFI_DT_INNER_ORDER_REQ_I_REQUE 的类型 I_REQUEST 不活动 

例程 EXPAND (无法扩展表 ZFI_DT_INNER_ORDER_REQ) 
组件 ZFI_DT_INNER_ORDER_RSP_E_RESPO 的类型 E_RESPONSE 不活动 

例程 EXPAND (无法扩展表 ZFI_DT_INNER_ORDER_RSP) 
ZMMS_QH1_K2_ZSQID_ITEMS-NET_AMOUNT（组合索引表/字段 ZMMT_SETTLE_HEAD-WAERS 不存在或数据类型错误） 

ZMMS_QH1_K2_ZSQID_ITEMS-ORD_PRICE（组合索引表/字段 ZMMT_SETTLE_HEAD-WAERS 不存在或数据类型错误） 

ZMMS_QH1_K2_ZSQID_ITEMS-OTH_PRICE（组合索引表/字段 ZMMT_SETTLE_HEAD-WAERS 不存在或数据类型错误） 

ZMMS_QH1_K2_ZSQID_ITEMS-SETL_PRICE（组合索引表/字段 ZMMT_SETTLE_HEAD-WAERS 不存在或数据类型错误） 

ZMMS_QH1_K2_ZSQID_ITEMS-SETTLE_QTY（组合索引表/字段 ZMMT_SETTLE_ITEM-MEINS 不存在或数据类型错误） 

ZMMS_QH1_K2_ZSQID_ITEMS-TAX_AMOUNT（组合索引表/字段 ZMMT_SETTLE_HEAD-WAERS 不存在或数据类型错误） 

ZMMS_QH1_K2_ZSQID_ITEMS-TOTAL_AMOUNT（组合索引表/字段 ZMMT_SETTLE_HEAD-WAERS 不存在或数据类型错误） 

组件 ZQH_DT_E3050_GR_REQ_I_REQUEST 的类型 I_REQUEST 不活动 

例程 EXPAND (无法扩展表 ZQH_DT_E3050_GR_REQ) 

 组件 ZQH_DT_E3060_GI_REQ_I_REQUEST 的类型 I_REQUEST 不活动 

例程 EXPAND (无法扩展表 ZQH_DT_E3060_GI_REQ) 

 组件 ZQH_DT_E3070_GR_CONTRACT_REQ_I 的类型 I_REQUEST 不活动 

例程 EXPAND (无法扩展表 ZQH_DT_E3070_GR_CONTRACT_REQ) 

 组件 ZQH_DT_E3070_GR_CONTRACT_RSP_E 的类型 E_RESPONSE 不活动 

例程 EXPAND (无法扩展表 ZQH_DT_E3070_GR_CONTRACT_RSP) 

组件 ZFI_DT_INNER_ORDER_REQ 的类型 MT_INNER_ORDER_REQ 不活动 

例程 EXPAND (无法扩展表 ZFI_MT_INNER_ORDER_REQ) 

组件 ZFI_DT_INNER_ORDER_RSP 的类型 MT_INNER_ORDER_RSP 不活动 

例程 EXPAND (无法扩展表 ZFI_MT_INNER_ORDER_RSP) 
表类型 ZMMS_QH1_K2_ZSQID_ITEM 不能被激活 
行类型 ZMMS_QH1_K2_ZSQID_ITEMS 不活动或不存在 
组件 ZQH_DT_E3050_GR_REQ 的类型 MT_E3050_GR_REQ 不活动 

例程 EXPAND (无法扩展表 ZQH_MT_E3050_GR_REQ) 

组件 ZQH_DT_E3060_GI_REQ 的类型 MT_E3060_GI_REQ 不活动 

例程 EXPAND (无法扩展表 ZQH_MT_E3060_GI_REQ) 

组件 ZQH_DT_E3070_GR_CONTRACT_REQ 的类型 MT_E3070_GR_CONTRACT_REQ 不活动 

例程 EXPAND (无法扩展表 ZQH_MT_E3070_GR_CONTRACT_REQ) 

组件 ZQH_DT_E3070_GR_CONTRACT_RSP 的类型 MT_E3070_GR_CONTRACT_RSP 不活动 

例程 EXPAND (无法扩展表 ZQH_MT_E3070_GR_CONTRACT_RSP) 
搜索帮助 ZFI_HELP_PAY_BANK_ACCOUNT 不能被激活 

搜索帮助 ZSH_BSMARK 不能被激活 

搜索帮助 ZSH_CREDIT 不能被激活 

搜索帮助 ZSH_TRADE 不能被激活 

表 CI_EKKODBX 不能被激活 

表 CI_EKPODB 不能被激活 

表 ZABAPQH_VBAP 不能被激活 

表 ZALSMEX_TABLINE3 不能被激活 

表 ZBVBAPKOZ 不能被激活 

表 ZFIS_ACCOUNT_BALANCE_A02 不能被激活 

表 ZFIS_ACCOUNT_BALANCE_DETAIL_A2 不能被激活 

表 ZFIS_BOE_FEEDBACK_PROC 不能被激活 

表 ZFIT_BANK_MAP 不能被激活 

表 ZFIT_BSMARK 不能被激活 

表 ZFIT_DOC_RULE 不能被激活 

表 ZFIT_DOC_SUBST 不能被激活 

表 ZFIT_R105_HCONF 不能被激活 

表 ZFIT_TRADE_TYPE 不能被激活 

表 ZFI_DT_INNER_ORDER_REQ 不能被激活 

表 ZFI_DT_INNER_ORDER_REQ_I_REQUE 不能被激活 

表 ZFI_DT_INNER_ORDER_RSP 不能被激活 

表 ZFI_DT_INNER_ORDER_RSP_E_RESPO 不能被激活 

表 ZFI_MT_INNER_ORDER_REQ 不能被激活 

表 ZFI_MT_INNER_ORDER_RSP 不能被激活 

表 ZKOMP_APPEND 不能被激活 

表 ZMMS_MB51 不能被激活 

表 ZMMS_QH1_K2_ZSQID_ITEMS 不能被激活 

表 ZMMS_SQID 不能被激活 

表 ZMMT_CREDIT_RATE 不能被激活 

表 ZMMT_GXDQ_MAT 不能被激活 

表 ZMMT_SETTLE_HEAD 不能被激活 

表 ZMMT_SETTLE_ITEM 不能被激活 

表 ZQH_DT_E3050_GR_REQ 不能被激活 

表 ZQH_DT_E3050_GR_REQ_I_REQUEST 不能被激活 

表 ZQH_DT_E3050_GR_RSP 不能被激活 

表 ZQH_DT_E3050_GR_RSP_E_RESPONSE 不能被激活 

表 ZQH_DT_E3060_GI_REQ 不能被激活 

表 ZQH_DT_E3060_GI_REQ_I_REQUEST 不能被激活 

表 ZQH_DT_E3060_GI_RSP 不能被激活 

表 ZQH_DT_E3060_GI_RSP_E_RESPONSE 不能被激活 

表 ZQH_DT_E3070_GR_CONTRACT_REQ 不能被激活 

表 ZQH_DT_E3070_GR_CONTRACT_REQ_I 不能被激活 

表 ZQH_DT_E3070_GR_CONTRACT_RSP 不能被激活 

表 ZQH_DT_E3070_GR_CONTRACT_RSP_E 不能被激活 

表 ZQH_MT_E3050_GR_REQ 不能被激活 

表 ZQH_MT_E3050_GR_RSP 不能被激活 

表 ZQH_MT_E3060_GI_REQ 不能被激活 

表 ZQH_MT_E3060_GI_RSP 不能被激活 

表 ZQH_MT_E3070_GR_CONTRACT_REQ 不能被激活 

表 ZQH_MT_E3070_GR_CONTRACT_RSP 不能被激活 

表 ZSQH1_MM_001 不能被激活 

