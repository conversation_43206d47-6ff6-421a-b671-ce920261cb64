*&---------------------------------------------------------------------*
*&   包含                           ZMMD033_DEFINE
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& DEVELOPER            < 开  发>: 胡洪铭
*& CREATE ON            <创建日期>:  20230828
*& FS NUMBER            <FS 编号>:
*& FUNCTIONAL CONSULTANT<功能顾问>:
*& DESCRIPTION          <FS 中业务需求概述>:
*&
*&---------------------------------------------------------------------*
*              MODIFICATION LOG<程序修改日志,创建时不要填写>
*<版本>       <日期>        <开发者>     <功能顾问>   任务编号    <请求号>
*VERSION      DATE        PROGRAMMER   CORR. #       IL#        TRANSPORT
*   1         YYYY/MM/DD
*DESCRIPTION<程序逻辑修改 版本1> :
*
*DESCRIPTION<程序逻辑修改 版本2> :
*
*&---------------------------------------------------------------------*

************************************************************************
*            <第一部分---声明程序变量和类>  DECLARATION                 *
************************************************************************
*----------------------------------------------------------------------*
* <1.1-声明常数> CONSTANTS DECLARATION                                 *
*----------------------------------------------------------------------*


*----------------------------------------------------------------------*
* <1.2-声明用户自定义数据类型> LOCAL DATA TYPES IN PROGRAM              *
*----------------------------------------------------------------------*
TYPES:BEGIN OF gty_alv,
box         TYPE c,                                       "选择
zsqid       TYPE zmmt_settle_head-zsqid,                  "结算申请
zstatus     TYPE zmmt_settle_head-zsqid,                  "状态
zstatus_txt TYPE char20,                                  "状态文本
k2id        TYPE zmmt_settle_head-k2id,                   " K2ID
zzcont      TYPE zmmt_settle_head-zzcont,                 "合同编号
buname      TYPE but000-mc_name1,                         "客商编码
buname_txt  TYPE char30,                                  "客商名称
order_no    TYPE zmmt_settle_item-order_no,               "订单编号
order_item  TYPE zmmt_settle_item-order_item,             "订单项目
werks       TYPE zmmt_settle_item-werks,                  "工厂
maktx       TYPE zmmt_settle_head-class_name,             "品类名称
zzkwmeng    TYPE zmmt_htys_item-zzkwmeng,                 "合同吨数
menge       TYPE zmmt_settle_item-settle_qty,             "订单数量
gewei       TYPE ekpo-gewei,                              "重量单位2
unit        TYPE char4,                                   "重量单位
meins       TYPE zmmt_settle_head-meins,                  "价格单位
sqty1       TYPE zmmt_settle_item-settle_qty,             "已结算数量
sqty2       TYPE zmmt_settle_item-settle_qty,             "可结算数量
vbeln       TYPE zmmt_settle_item-vbeln,                  "交货单
posnr       TYPE zmmt_settle_item-posnr,                  "交货项目
lgort       TYPE zmmt_settle_item-lgort,                  "库存地点
lgobe       TYPE char16,                                  "仓库名称
charg       TYPE zmmt_settle_item-charg,                   "SAP批次
z_batch_011 TYPE zmmt_settle_item-z_batch_011,            "到货批次
lfimg       TYPE zmmt_settle_item-settle_qty,             "交货数量
clabs       TYPE zmmt_settle_item-settle_qty,             "库存数量
item_no     TYPE zmmt_settle_item-item_no,                "行项目号
zcnam       TYPE zmmt_settle_head-zcnam,                  "创建人
zcdat       TYPE zmmt_settle_head-zcdat,                  "创建日期
zctim       TYPE zmmt_settle_head-zctim,                  "创建时间
zunam       TYPE zmmt_settle_head-zunam,                  "最后修改人
zudat       TYPE zmmt_settle_head-zudat,                  "更改日期
zutim       TYPE zmmt_settle_head-zutim,                  "更改时间
zzgxfx      TYPE zmmt_settle_head-zzgxfx,                 "购销方向
END OF gty_alv.
TYPES gtty_alv TYPE STANDARD TABLE OF gty_alv.

"结算申请
TYPES: BEGIN OF ty_item.
 INCLUDE TYPE zmmt_settle_item.
 TYPES: box      TYPE c,
 tax_rate TYPE zmmt_settle_item-setl_price,     "税率
 lgobe    TYPE char16,                          "仓库名称
 menge    TYPE zmmt_settle_item-settle_qty,     "订单数量
 sqty1    TYPE zmmt_settle_item-settle_qty,
END OF ty_item.

" 结算申请抬头
TYPES: BEGIN OF ty_settle_head.
 INCLUDE TYPE zmmt_settle_head.
 TYPES: "buname      TYPE but000-mc_name1,
 butxt       TYPE zmmt_htys_head-butxt,
 zzcont1     TYPE zmmt_jyys_head-zzcont1,
 zconame     TYPE zmmt_jyys_head-zconame,
"        maktx       TYPE zmmt_htys_item-maktx,
 settle_unit TYPE zmmt_settle_item-meins,
 zzitem      TYPE zmmt_jyys_head-zitem,
 zzitemt     TYPE zmmt_jyys_head-zzitemt,
 zvkgrp_txt  TYPE zmmt_htys_head-zvkgrp_txt,
 xgsht       TYPE zmmt_htys_head-xgsht,
 xcdfw       TYPE zmmt_htys_head-xcdfw,
END OF ty_settle_head.

*----------------------------------------------------------------------*
* <1.3-声明内表、工作区> GLOBAL INTERNAL TABLES 、WORK AREA DECLARATION *
*----------------------------------------------------------------------*
DATA:gt_alv      TYPE STANDARD TABLE OF gty_alv,
gt_fieldcat TYPE lvc_t_fcat.

*----------------------------------------------------------------------*
* <1.4-声明全局变量> GLOBAL VARIANTS DECLARATION                        *
*----------------------------------------------------------------------*
DATA: gs_head TYPE ty_settle_head.
DATA: gv_okcode TYPE sy-ucomm.
DATA: gt_headdata TYPE TABLE OF zmmt_settle_head.
DATA: gt_itemdata TYPE TABLE OF zmmt_settle_item.
DATA: gt_itemdel  TYPE TABLE OF ty_item.  "9000屏幕行项目del
DATA: gt_item TYPE TABLE OF ty_item.      "9000屏幕行项目
DATA: gs_item TYPE ty_item.
DATA: gv_atyp TYPE c.           "全局状态 1 修改、2 显示
DATA: gs_headdata TYPE ty_settle_head.
DATA: gt_jsdata_item TYPE TABLE OF ty_item.
DATA: gs_jsdata_item TYPE ty_item.
DATA: gv_code9000 TYPE sy-ucomm.
" 购销方向range表
DATA: gr_gxfx   TYPE RANGE OF zmmt_settle_head-zzgxfx.
" 结算类型range表
DATA: gr_settyp TYPE RANGE OF zmmt_settle_head-settle_typ.
"ALV
DATA:
gv_code     TYPE sy-ucomm,
gv_col      LIKE sy-cucol VALUE 1,
gs_fieldcat TYPE lvc_s_fcat,
*  gt_fieldcat TYPE lvc_t_fcat,
gs_layout   TYPE lvc_s_layo,
gt_style    TYPE TABLE OF lvc_s_styl WITH HEADER LINE.

"文本控件
DATA go_container TYPE REF TO cl_gui_custom_container.
DATA go_editor TYPE REF TO cl_gui_textedit.
DATA:gv_init,
*      gt_m1(256)   TYPE c OCCURS 0,
gt_m1        TYPE STANDARD TABLE OF line,
gs_line(256) TYPE c.



*----------------------------------------------------------------------*
* <1.5-声明字段符号> FIELD-SYMBOLS DECLARATION                          *
*----------------------------------------------------------------------*


*----------------------------------------------------------------------*
* <1.6-声明控件 OO对象> ALV TABLECONTRL                                 *
*----------------------------------------------------------------------*
DATA:
gcl_alvgrid   TYPE REF TO cl_gui_alv_grid.
"GCL_CONTAINER TYPE REF TO CL_GUI_CONTAINER,
"GCL_SPLITTER  TYPE REF TO CL_GUI_SPLITTER_CONTAINER.


*----------------------------------------------------------------------*
* <1.7-类定义>                                                         *
*----------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& LCL_CLASS1                                                          *
*&---------------------------------------------------------------------*
*& 类功能说明
*&---------------------------------------------------------------------*
CLASS lcl_event_handler DEFINITION.
PUBLIC SECTION.

METHODS on_f4 FOR EVENT onf4 OF cl_gui_alv_grid
IMPORTING e_fieldname
        es_row_no
        er_event_data .

METHODS data_changed FOR EVENT data_changed OF cl_gui_alv_grid
IMPORTING er_data_changed
        e_onf4
        e_onf4_before
        e_onf4_after
        e_ucomm.

METHODS data_changed_finished
        FOR EVENT data_changed_finished OF cl_gui_alv_grid
IMPORTING e_modified et_good_cells.

ENDCLASS.
*----------------------------------------------------------------------*
* <1.8-类实现>                                                         *
*----------------------------------------------------------------------*
CLASS lcl_event_handler IMPLEMENTATION.

METHOD on_f4.
er_event_data->m_event_handled = 'X' .
*     PERFORM frm_handle_f4 USING e_fieldname
*                                  es_row_no
*                                  er_event_data .
ENDMETHOD.

METHOD data_changed.
"      PERFORM frm_data_changed USING er_data_changed.
ENDMETHOD.

METHOD data_changed_finished.
"     PERFORM frm_data_changed_finished TABLES et_good_cells.
ENDMETHOD.
ENDCLASS.

DATA gcl_event TYPE REF TO lcl_event_handler.

*----------------------------------------------------------------------*
* <1.8-类实现>                                                         *
*----------------------------------------------------------------------*


DATA: c_flag TYPE c.