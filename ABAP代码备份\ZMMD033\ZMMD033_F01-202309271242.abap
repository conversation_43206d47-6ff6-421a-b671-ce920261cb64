*&---------------------------------------------------------------------*
*&   包含                           ZMMD033_F01
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Developer            < 开  发>: 胡洪铭
*& Create on            <创建日期>: 20230828
*& FS NUMBER            <FS 编号>:
*& Functional Consultant<功能顾问>: 白仁峰
*& Description          <FS 中业务需求概述>:
*&
*&---------------------------------------------------------------------*
*              MODIFICATION LOG<程序修改日志,创建时不要填写>
*<版本>       <日期>        <开发者>     <功能顾问>   任务编号    <请求号>
*Version      Date        Programmer   Corr. #       IL#        Transport
*   1         YYYY/MM/DD
*Description<程序逻辑修改 版本1> :
*
*Description<程序逻辑修改 版本2> :


*&---------------------------------------------------------------------*
*& Form FRM_GET_DATA
*&---------------------------------------------------------------------*
*& 获取数据 get data
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_get_data.
    DATA: ls_alv TYPE gty_alv.
    DATA: lv_lines TYPE char20.
    DATA: lv_objek TYPE cuobn.
    DATA lt_ftaxp TYPE TABLE OF ftaxp.
  
    TYPES: BEGIN OF ty_objek,
             objek TYPE cuobn,
           END OF ty_objek.
    DATA:ls_objek TYPE ty_objek.
  
    " mcha表
    TYPES: BEGIN OF ty_mcha,
             matnr TYPE ekpo-matnr,
             werks TYPE ekpo-werks,
             charg TYPE ekes-charg,
             objek TYPE cuobn,
           END OF ty_mcha.
    DATA:ls_mcha TYPE ty_mcha.
    DATA:lt_mcha1 TYPE TABLE OF ty_mcha.
    DATA:ls_mcha1 TYPE ty_mcha.
  
    " ekes表
    TYPES: BEGIN OF ty_ekes,
             zzcont      TYPE  zmmt_jyys_head-zzcont,
             partner     TYPE  zmmt_jyys_head-partner,
             name1       TYPE  zmmt_jyys_head-name1,
             class2_name TYPE  zmmt_jyys_head-class2_name,
             ebeln       TYPE  ekpo-ebeln,
             ebelp       TYPE  ekpo-ebelp,
             werks       TYPE  ekpo-werks,
             matnr       TYPE  ekpo-matnr,
             meins       TYPE  ekpo-meins,
             vbeln       TYPE  ekes-vbeln,
             vbelp       TYPE  ekes-vbelp,
             menge       TYPE  ekes-menge,
             charg       TYPE  ekes-charg,
             objek       TYPE cuobn,
           END OF ty_ekes.
    DATA: ls_ekes TYPE ty_ekes.
    DATA: lt_ekes TYPE TABLE OF ty_ekes.
  
    " cont表
    TYPES: BEGIN OF ty_cont,
             zzcont      TYPE  zmmt_jyys_head-zzcont,
             partner     TYPE  zmmt_jyys_head-partner,
             class2_name TYPE  zmmt_jyys_head-class2_name,
             name1       TYPE  zmmt_jyys_head-name1,
             waers       TYPE  zmmt_settle_head-waers,
             matnr       TYPE  zmmt_jyys_item-matnr,
             maktx       TYPE  zmmt_jyys_item-maktx,
             gewei       TYPE  mara-gewei,
             zzkwmeng    TYPE  zmmt_jyys_item-zzkwmeng,
           END OF ty_cont.
    DATA: ls_cont TYPE ty_cont.
    DATA: lt_cont TYPE TABLE OF ty_cont.
  
    DATA: lt_dd07t_zstatus TYPE STANDARD TABLE OF dd07t,
          ls_dd07t_zstatus LIKE LINE OF lt_dd07t_zstatus.
  
    " 公司代码必输
    IF p_bukrs IS INITIAL.
      MESSAGE '公司代码必输' TYPE 'S' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING .
    ENDIF.
  
    CLEAR:gt_headdata,gt_itemdata.
    "查询结算单清单
    SELECT * FROM zmmt_settle_head
    WHERE bukrs  = @p_bukrs          " 公司代码
      AND zzgxfx = @p_gxfx           " 购销方向
      AND settle_typ = @p_stltyp     " 结算类型
      AND zzcont  IN @s_cont         " 合同编号
      AND partner IN @s_patner       " 客商编码
      AND matnr   IN @s_matnr        " 物料编号
      AND zsqid   IN @s_zsqid        " 结算申请
      AND zcdat   IN @s_grdate       " 创建日期
     INTO CORRESPONDING FIELDS OF TABLE @gt_headdata.
  
    SELECT * FROM zmmt_settle_item
    FOR ALL ENTRIES IN @gt_headdata
    WHERE zsqid = @gt_headdata-zsqid
    INTO CORRESPONDING FIELDS OF TABLE @gt_itemdata.
  
    " 当不勾选【按批次结算】时
    IF p_pcjs IS INITIAL.
      " 购销方向为采购 = ‘B’
      IF p_gxfx = 'B'.
        SELECT h~zzcont,       "合同编号 
               h~partner,      "供应商编号
               h~name1,        "供应商名称 
               h~class2_name,  "品类名称 
               p~ebeln,        "采购订单号 
               p~ebelp,        "行项目
               p~werks,        "工厂 
               p~matnr,        "物料 
               p~menge,        "订单数量
               p~meins,        "订单单位
               p~brtwr,        "含税金额
               p~peinh         "价格单位 
        FROM ekpo AS p
        INNER JOIN ekko AS k ON p~ebeln = k~ebeln
        INNER JOIN zmmt_jyys_head AS h ON k~zzcont = h~zzcont
                                      AND k~bukrs = h~bukrs
        WHERE h~bukrs  = @p_bukrs
          AND h~zzgxfx = @p_gxfx
          AND h~partner IN @s_patner
          AND h~zzcont IN @s_cont
          AND p~ebeln IN @s_order
          AND p~matnr IN @s_matnr
        INTO TABLE @DATA(lt_ekpo).
  
        PERFORM frm_get_domain_txt TABLES lt_dd07t_zstatus
                                   USING 'ZDZSTATUS'.
        SORT lt_dd07t_zstatus BY domvalue_l.
  
        " 数据整理
        LOOP AT lt_ekpo INTO DATA(ls_ekpo).
  *        MOVE-CORRESPONDING ls_ekpo TO ls_alv.
          ls_alv-zzcont = ls_ekpo-zzcont.
          ls_alv-buname = ls_ekpo-partner.
  *        ls_alv- = ls_ekpo-name1.
          ls_alv-maktx = ls_ekpo-class2_name.
          ls_alv-order_no = ls_ekpo-ebeln.
          ls_alv-order_item = ls_ekpo-ebelp.
          ls_alv-werks = ls_ekpo-werks.
  *        ls_alv- = ls_ekpo-matnr.
          ls_alv-menge = ls_ekpo-menge.
          ls_alv-gewei = ls_ekpo-meins.
          " 抬头数据
          READ TABLE gt_headdata INTO DATA(ls_head) WITH KEY zzcont = ls_ekpo-zzcont.
          IF sy-subrc = 0.
            MOVE-CORRESPONDING ls_head TO ls_alv.
          ENDIF.
          " 物料编号
  *        ls_alv-matnr = ls_ekpo-matnr.
          " 物料描述
          IF ls_alv-maktx IS INITIAL.
            SELECT SINGLE maktx FROM makt
              WHERE matnr = @ls_ekpo-matnr
                AND spras = '1'
              INTO @ls_alv-maktx.
          ENDIF.
          " 合同编号
          ls_alv-zzcont = ls_ekpo-zzcont.
          " 合同吨数
          SELECT SUM( b~zzkwmeng ) AS zzkwmeng1,
                 b~zzcont
            FROM zmmt_jyys_item AS b
            WHERE b~zzcont = @ls_ekpo-zzcont
             GROUP BY b~zzcont
             INTO @DATA(lv_zzkwmeng).
          ENDSELECT.
  
          ls_alv-zzkwmeng = lv_zzkwmeng-zzkwmeng1.
          " 已结算数量，结算单位
          SELECT b~settle_qty,
                 a~meins
            FROM zmmt_settle_head AS a
            INNER JOIN zmmt_settle_item AS b ON a~zsqid = b~zsqid
            WHERE a~zzcont = @ls_ekpo-zzcont
              AND a~zstatus <> '05'
              AND a~zstatus <> '40'
             INTO @DATA(lv_qty).
          ENDSELECT.
  
          ls_alv-sqty1 = lv_qty-settle_qty.
  
          " 重量单位
          SELECT SINGLE gewei,
                        matnr
            FROM mara
            WHERE matnr = @ls_ekpo-matnr
            INTO @DATA(lv_gewei).
  
  
          ls_alv-gewei = lv_gewei-gewei.
          " 重量单位
          ls_alv-unit = lv_gewei-gewei.
          " 可结算数量
          " 单位换算
          IF ls_alv-zzkwmeng IS NOT INITIAL AND ls_alv-menge IS NOT INITIAL.
            IF ls_alv-gewei = 'KG'.
              ls_alv-menge = ls_alv-zzkwmeng * 1000.
              ls_alv-meins = 1000.
            ELSEIF ls_alv-gewei = 'A75' OR ls_alv-gewei = '百克' .
              ls_alv-menge = ls_alv-zzkwmeng * 10000.
              ls_alv-meins = 10000.
            ELSE.
              ls_alv-meins = 1.
              ls_alv-menge = ls_alv-zzkwmeng.
            ENDIF.
          ELSEIF ls_alv-menge IS NOT INITIAL AND ls_alv-zzkwmeng IS INITIAL.
            IF ls_alv-gewei = 'TO'.
              ls_alv-zzkwmeng = ls_alv-menge.
            ELSEIF ls_alv-gewei = 'KG'.
              ls_alv-zzkwmeng = ls_alv-menge / 1000.
            ELSEIF ls_alv-gewei = 'A75' OR ls_alv-gewei = '百克' .
              ls_alv-zzkwmeng = ls_alv-menge / 10000.
            ENDIF.
          ENDIF.
  
          ls_alv-sqty2 = ls_alv-menge - ls_alv-sqty1.
  
          " 税率计算，由税码转换成税率
          SELECT SINGLE
                 a~mwskz
            FROM ekpo AS a
            INNER JOIN ekko AS b ON a~ebeln = b~ebeln
             WHERE a~ebeln = @ls_alv-order_no
               AND a~ebelp = @ls_alv-order_item
             INTO @DATA(lv_mwskz).
  
          IF sy-subrc = 0.
            gs_item-mwskz = lv_mwskz.
          ENDIF.
  
          " 税码
          gs_item-mwskz = lv_mwskz.
          " 税率
          IF gs_item-mwskz IS NOT INITIAL .
            CALL FUNCTION 'GET_TAX_PERCENTAGE'
              EXPORTING
                aland   = 'CN'
                datab   = sy-datum
                mwskz   = gs_item-mwskz
                txjcd   = '1'
              TABLES
                t_ftaxp = lt_ftaxp.
            READ TABLE lt_ftaxp INTO DATA(ls_ftaxp) WITH KEY kschl = 'MWVS'.
            IF sy-subrc = 0.
              gs_item-tax_rate = ls_ftaxp-kbetr / 10.
              CLEAR:ls_ftaxp,lt_ftaxp.
            ENDIF.
          ENDIF.
          " 结算单价
          gs_item-setl_price = gs_item-ord_price + gs_item-oth_price.
          " 含税金额计算
          gs_item-total_amount = gs_item-settle_qty * gs_item-setl_price.
          " 不含税金额计算
          gs_item-net_amount = gs_item-total_amount / ( 1 + gs_item-tax_rate ).
          " 税额计算
          gs_item-tax_amount = gs_item-total_amount - gs_item-net_amount.
  
          "输出alv界面
          APPEND ls_alv TO gt_alv.
          " 回写表
          MOVE-CORRESPONDING ls_alv TO gs_item.
          " 结算单位
          gs_item-meins = ls_alv-unit.
          APPEND gs_item TO gt_item.
          CLEAR ls_ekpo.
          CLEAR ls_alv.
        ENDLOOP.
  
  
        " 购销方向为销售 = ‘S’
      ELSEIF p_gxfx = 'S'.
        SELECT h~zzcont,
             h~partner,
             h~name1,
             h~class2_name,
             p~vbeln,
             p~posnr,
             p~werks,
             p~matnr,
             p~kwmeng,
             p~vrkme
        FROM vbap AS p
        INNER JOIN vbak AS k ON p~vbeln = k~vbeln
        INNER JOIN vbkd AS d ON p~vbeln = d~vbeln
        INNER JOIN zmmt_jyys_head AS h ON  d~bstkd = h~zzcont
  *                                       AND d~bukrs = h~bukrs
        WHERE h~bukrs = @p_bukrs
          AND h~zzgxfx = @p_gxfx
          AND h~partner IN @s_patner
          AND p~vbeln IN @s_order
          AND h~zzcont IN @s_cont
  *          AND p~ebelp IN @s_order
          AND p~matnr IN @s_matnr
          AND d~posnr = ''
        INTO TABLE @DATA(lt_vbap).
  
        PERFORM frm_get_domain_txt TABLES lt_dd07t_zstatus
                                   USING 'ZDZSTATUS'.
        SORT lt_dd07t_zstatus BY domvalue_l.
  
        " 数据整理
        LOOP AT lt_vbap INTO DATA(ls_vbap).
          ls_alv-zzcont     = ls_vbap-zzcont.
          ls_alv-buname     = ls_vbap-partner.
          ls_alv-maktx      = ls_vbap-class2_name.
          ls_alv-order_no   = ls_vbap-vbeln.
          ls_alv-order_item = ls_vbap-posnr.
          ls_alv-werks      = ls_vbap-werks.
          ls_alv-menge      = ls_vbap-kwmeng.
          ls_alv-meins      = ls_vbap-vrkme.
  
          " 抬头数据
          READ TABLE gt_headdata INTO DATA(ls_head1) WITH KEY zzcont = ls_vbap-zzcont.
          IF sy-subrc = 0.
            MOVE-CORRESPONDING ls_head TO ls_alv.
          ENDIF.
          " 物料编号
  *       ls_alv-matnr = ls_vbap-matnr.
          " 物料描述
          IF ls_alv-maktx IS INITIAL.
            SELECT SINGLE maktx FROM makt
              WHERE matnr = @ls_vbap-matnr
                AND spras = '1'
              INTO @ls_alv-maktx.
          ENDIF.
          " 合同编号
          ls_alv-zzcont = ls_vbap-zzcont.
          " 合同吨数
          SELECT SINGLE
                 SUM( b~zzkwmeng ) AS zzkwmeng1,
                 b~zzcont
            FROM zmmt_jyys_item AS b
            WHERE b~zzcont = @ls_vbap-zzcont
             GROUP BY b~zzcont
             INTO @DATA(lv_zzkwmeng1).
  
          ls_alv-zzkwmeng = lv_zzkwmeng1-zzkwmeng1.
          " 已结算数量，结算单位
          SELECT SINGLE
                 b~settle_qty,
                 a~meins
            FROM zmmt_settle_head AS a
            INNER JOIN zmmt_settle_item AS b ON a~zsqid = b~zsqid
            WHERE a~zzcont = @ls_vbap-zzcont
              AND a~zstatus <> '05'
              AND a~zstatus <> '40'
             INTO @DATA(lv_qty2).
  
          ls_alv-sqty1 = lv_qty2-settle_qty.
          ls_alv-meins = lv_qty2-meins.
          " 重量单位
          SELECT matnr,
                 gewei
            FROM mara
            WHERE matnr = @ls_vbap-matnr
            INTO @DATA(lv_gewei1).
          ENDSELECT.
  
          ls_alv-gewei = lv_gewei1-gewei.
          " 重量单位
          ls_alv-unit = lv_gewei1-gewei.
          " 可结算数量
          " 单位换算
          IF ls_alv-zzkwmeng IS NOT INITIAL AND ls_alv-menge IS NOT INITIAL.
            IF ls_alv-gewei = 'KG'.
              ls_alv-menge = ls_alv-zzkwmeng * 1000.
              ls_alv-meins = 1000.
            ELSEIF ls_alv-gewei = 'A75' OR ls_alv-gewei = '百克' .
              ls_alv-menge = ls_alv-zzkwmeng * 10000.
              ls_alv-meins = 10000.
            ELSE.
              ls_alv-meins = 1.
              ls_alv-menge = ls_alv-zzkwmeng.
            ENDIF.
          ELSEIF ls_alv-menge IS NOT INITIAL AND ls_alv-zzkwmeng IS INITIAL.
            IF ls_alv-gewei = 'TO'.
              ls_alv-zzkwmeng = ls_alv-menge.
            ELSEIF ls_alv-gewei = 'KG'.
              ls_alv-zzkwmeng = ls_alv-menge / 1000.
            ELSEIF ls_alv-gewei = 'A75' OR ls_alv-gewei = '百克' .
              ls_alv-zzkwmeng = ls_alv-menge / 10000.
            ENDIF.
          ENDIF.
  
          ls_alv-sqty2 = ls_alv-menge - ls_alv-sqty1.
  
          " 税率计算，由税码转换成税率
          DATA lv_datab TYPE a002-datab.
          lv_datab = sy-datum.
          SELECT b~mwsk1
            FROM a002 AS a
            INNER JOIN konp AS b ON a~knumh = b~knumh
            WHERE a~aland = 'CN'
              AND a~datbi >= @lv_datab
              AND a~datab <= @lv_datab
              AND a~taxk1 = '1'
            INTO @DATA(lv_mwskz1).
          ENDSELECT.
          " 税码
          IF sy-subrc = 0.
            gs_item-mwskz = lv_mwskz1.
          ENDIF.
  
  
          " 税率
          IF gs_item-mwskz IS NOT INITIAL .
            CALL FUNCTION 'GET_TAX_PERCENTAGE'
              EXPORTING
                aland   = 'CN'
                datab   = sy-datum
                mwskz   = gs_item-mwskz
                txjcd   = '1'
              TABLES
                t_ftaxp = lt_ftaxp.
            READ TABLE lt_ftaxp INTO DATA(ls_ftaxp1) WITH KEY kschl = 'MWVS'.
            IF sy-subrc = 0.
              gs_item-tax_rate = ls_ftaxp1-kbetr / 10.
              CLEAR:ls_ftaxp1,lt_ftaxp.
            ENDIF.
          ENDIF.
          " 结算单价
          gs_item-setl_price = gs_item-ord_price + gs_item-oth_price.
          " 含税金额计算
          gs_item-total_amount = gs_item-settle_qty * gs_item-setl_price.
          " 不含税金额计算
          gs_item-net_amount = gs_item-total_amount / ( 1 + gs_item-tax_rate ).
          " 税额计算
          gs_item-tax_amount = gs_item-total_amount - gs_item-net_amount.
          "输出alv界面
          APPEND ls_alv TO gt_alv.
  
          " 回写表
          MOVE-CORRESPONDING ls_alv TO gs_item.
          " 结算单位
          gs_item-meins = ls_alv-unit.
          APPEND gs_item TO gt_item.
          CLEAR ls_vbap.
          CLEAR ls_alv.
        ENDLOOP.
  
      ENDIF.
      " 勾选【按批次结算】时
    ELSE.
      " 购销方向为采购 = ‘B’
      IF p_gxfx = 'B'.
        SELECT h~zzcont,
             h~partner,
             h~name1,
             h~class2_name,
             p~ebeln,
             p~ebelp,
             p~werks,
             p~matnr,
             p~meins,
             e~vbeln,
             e~vbelp,
             e~menge,
             e~charg
        FROM ekes AS e
        INNER JOIN ekpo AS p ON e~ebeln = p~ebeln
                            AND e~ebelp = p~ebelp
        INNER JOIN ekko AS k ON p~ebeln = k~ebeln
        INNER JOIN zmmt_jyys_head AS h ON k~zzcont = h~zzcont
                                      AND k~bukrs = h~bukrs
        WHERE h~bukrs = @p_bukrs
          AND h~zzcont = @p_gxfx
          AND h~partner IN @s_patner
          AND p~ebeln IN @s_order
          AND h~zzcont IN @s_cont
          AND p~matnr IN @s_matnr
          AND e~erdat IN @s_grdate
          AND e~menge <> 0
          AND e~charg <> ''
          AND e~loekz = ''
        INTO TABLE @lt_ekes.
  
        PERFORM frm_get_domain_txt TABLES lt_dd07t_zstatus
                                   USING 'ZDZSTATUS'.
        SORT lt_dd07t_zstatus BY domvalue_l.
  
  
        LOOP AT lt_ekes INTO ls_ekes.
          ls_alv-zzcont = ls_ekes-zzcont.
          ls_alv-buname = ls_ekes-partner.
  *        ls_alv- = ls_ekes-name1.
          ls_alv-maktx = ls_ekes-class2_name.
          ls_alv-order_no = ls_ekes-ebeln.
          ls_alv-order_item = ls_ekes-ebelp.
          ls_alv-werks = ls_ekes-werks.
  *        ls_alv- = ls_ekes-matnr.
          ls_alv-meins = ls_ekes-meins.
          ls_alv-vbeln = ls_ekes-vbeln.
          ls_alv-posnr = ls_ekes-vbelp.
          ls_alv-lfimg = ls_ekes-menge.
          ls_alv-z_batch_011 = ls_ekes-charg.
          " 抬头数据
          READ TABLE gt_headdata INTO DATA(ls_head2) WITH KEY zzcont = ls_ekes-zzcont.
          IF sy-subrc = 0.
            MOVE-CORRESPONDING ls_head2 TO ls_alv.
          ENDIF.
          " 物料描述
          IF ls_alv-maktx IS INITIAL.
            SELECT SINGLE maktx FROM makt
              WHERE matnr = @ls_ekes-matnr
                AND spras = '1'
              INTO @ls_alv-maktx.
          ENDIF.
          " 合同吨数
          SELECT SUM( b~zzkwmeng ) AS zzkwmeng1,
                 b~zzcont
            FROM zmmt_jyys_item AS b
            WHERE b~zzcont = @ls_ekes-zzcont
             GROUP BY b~zzcont
             INTO @DATA(lv_zzkwmeng2).
  
            ls_alv-zzkwmeng = lv_zzkwmeng2-zzkwmeng1.
            " 已结算数量，结算单位
            SELECT SUM( b~settle_qty ) AS sqty1,
                   a~meins
              FROM zmmt_settle_head AS a
              INNER JOIN zmmt_settle_item AS b ON a~zsqid = b~zsqid
              WHERE a~zzcont = @ls_ekes-zzcont
                AND a~zstatus <> '05'
                AND a~zstatus <> '40'
                GROUP BY a~meins
               INTO @DATA(lv_qty1).
            ENDSELECT.
  
            ls_alv-sqty1 = lv_qty1-sqty1.
            ls_alv-meins = lv_qty-meins.
            " 重量单位
            SELECT matnr,
                   gewei
              FROM mara
              WHERE matnr = @ls_ekes-matnr
              INTO @DATA(lv_gewei2).
            ENDSELECT.
            ls_alv-gewei = lv_gewei2-gewei.
            " 重量单位
            ls_alv-unit = lv_gewei2-gewei.
            " 可结算数量
            " 单位换算
            IF ls_alv-zzkwmeng IS NOT INITIAL AND ls_alv-menge IS NOT INITIAL.
              IF ls_alv-gewei = 'KG'.
                ls_alv-menge = ls_alv-zzkwmeng * 1000.
                ls_alv-meins = 1000.
              ELSEIF ls_alv-gewei = 'A75' OR ls_alv-gewei = '百克' .
                ls_alv-menge = ls_alv-zzkwmeng * 10000.
                ls_alv-meins = 10000.
              ELSE.
                ls_alv-meins = 1.
                ls_alv-menge = ls_alv-zzkwmeng.
              ENDIF.
            ELSEIF ls_alv-menge IS NOT INITIAL AND ls_alv-zzkwmeng IS INITIAL.
              IF ls_alv-gewei = 'TO'.
                ls_alv-zzkwmeng = ls_alv-menge.
              ELSEIF ls_alv-gewei = 'KG'.
                ls_alv-zzkwmeng = ls_alv-menge / 1000.
              ELSEIF ls_alv-gewei = 'A75' OR ls_alv-gewei = '百克'.
                ls_alv-zzkwmeng = ls_alv-menge / 10000.
              ENDIF.
            ENDIF.
  
  
            " ls_mcha表回写
            SELECT DISTINCT a~matnr,
                             a~werks,
                             a~charg
              FROM @lt_ekes AS a
              INTO TABLE @DATA(lt_mcha).
          ENDSELECT.
          " objek回写
          LOOP AT lt_ekes ASSIGNING FIELD-SYMBOL(<ls_mcha>).
            lv_objek = <ls_mcha>-matnr && <ls_mcha>-werks && <ls_mcha>-charg.
            <ls_mcha>-objek = lv_objek.
          ENDLOOP.
  
          "   取得主键
          SELECT a~matnr,
                  a~werks,
                  a~charg,
                  c~atwrt
           FROM @lt_ekes AS a
          INNER JOIN inob AS b ON a~objek = b~objek
          INNER JOIN ausp AS c ON b~cuobj = c~objek
          INNER JOIN cabn AS d ON c~atinn = d~atinn
            WHERE b~klart = '022'
              AND b~obtab = 'MCHA'
              AND d~atnam = 'Z_BATCH_011'
              AND c~atwrt IN @s_batch
            INTO TABLE @DATA(lt_ekes2).
          " 可结算数量
          ls_alv-sqty2 = ls_alv-menge - ls_alv-sqty1.
          " 税率计算，由税码转换成税率
          SELECT SINGLE
                 a~mwskz
            FROM ekpo AS a
            INNER JOIN ekko AS b ON a~ebeln = b~ebeln
             WHERE a~ebeln = @ls_alv-order_no
               AND a~ebelp = @ls_alv-order_item
             INTO @DATA(lv_mwskz2).
  
          IF sy-subrc = 0.
            gs_item-mwskz = lv_mwskz2.
          ENDIF.
  
          " 税码
          gs_item-mwskz = lv_mwskz.
          " 税率
          IF gs_item-mwskz IS NOT INITIAL .
            CALL FUNCTION 'GET_TAX_PERCENTAGE'
              EXPORTING
                aland   = 'CN'
                datab   = sy-datum
                mwskz   = gs_item-mwskz
                txjcd   = '1'
              TABLES
                t_ftaxp = lt_ftaxp.
            READ TABLE lt_ftaxp INTO DATA(ls_ftaxp2) WITH KEY kschl = 'MWVS'.
            IF sy-subrc = 0.
              gs_item-tax_rate = ls_ftaxp2-kbetr / 10.
              CLEAR:ls_ftaxp2,lt_ftaxp.
            ENDIF.
          ENDIF.
          " 结算单价
          gs_item-setl_price = gs_item-ord_price + gs_item-oth_price.
          " 含税金额计算
          gs_item-total_amount = gs_item-settle_qty * gs_item-setl_price.
          " 不含税金额计算
          gs_item-net_amount = gs_item-total_amount / ( 1 + gs_item-tax_rate ).
          " 税额计算
          gs_item-tax_amount = gs_item-total_amount - gs_item-net_amount.
  
          "输出alv界面
          APPEND ls_alv TO gt_alv.
          " 回写表
          MOVE-CORRESPONDING ls_alv TO gs_item.
          " 结算单位
          gs_item-meins = ls_alv-unit.
          MODIFY gt_item FROM gs_item.
          CLEAR ls_ekes.
          CLEAR ls_alv.
        ENDLOOP.
  
        " 购销方向为销售 =‘S’且结算类型 = ‘0’或‘1’
      ELSEIF p_gxfx = 'S' AND ( p_stltyp = '0' OR p_stltyp = '1' ).
        SELECT a~zzcont,
               a~partner,
               a~name1,
               a~class2_name,
               a~waers
            FROM zmmt_jyys_head AS a
            WHERE a~zzcont IN @s_cont
              AND a~zzgxfx = @p_gxfx
             INTO TABLE @lt_cont.
        "jjys
        LOOP AT lt_cont INTO ls_cont.
          SELECT SINGLE
                 a~zzcont,
                 a~matnr,
                 a~maktx,
                SUM( a~zzkwmeng ) AS zzkwmeng1,  " 合同吨数
                b~gewei
            FROM zmmt_jyys_item AS a
            INNER JOIN mara AS b ON a~matnr = b~matnr
            WHERE a~zzcont = @zmmt_jyys_head-zzcont
             AND a~matnr IN @s_matnr
             GROUP BY a~zzcont,
                       a~matnr,
                       a~maktx,
                       b~gewei
            INTO @DATA(ls_jyysitem).
          " 回写lt_cont
          ls_cont-matnr    = ls_jyysitem-matnr.
          ls_cont-maktx    = ls_jyysitem-maktx.
          ls_cont-zzkwmeng = ls_jyysitem-zzkwmeng1.
          ls_cont-gewei    = ls_jyysitem-gewei.
          MODIFY lt_cont FROM ls_cont.
  
          " mchb
          SELECT a~matnr,
               a~werks,
               a~lgort,
               a~charg,
               a~clabs
          FROM mchb AS a
          WHERE a~matnr IN @s_matnr
            AND a~matnr = @ls_cont-matnr
            AND a~werks = @p_werks
            AND a~lgort IN @s_lgort
            AND a~clabs <> 0
            INTO TABLE @DATA(lt_mchb).
  
          SELECT DISTINCT a~matnr,
                          a~werks,
                          a~charg
            FROM @lt_mchb AS a
            INTO TABLE @lt_mcha1.
  
          " objek回写
          LOOP AT lt_mcha1 ASSIGNING FIELD-SYMBOL(<ls_mcha1>).
            lv_objek = <ls_mcha1>-matnr && <ls_mcha1>-werks && <ls_mcha1>-charg.
            <ls_mcha1>-objek = lv_objek.
          ENDLOOP.
  
          " 取得主键
          SELECT  a~matnr,
                  a~werks,
                  a~charg,
                  c~atwrt
           FROM @lt_mcha1 AS a
          INNER JOIN inob AS b ON a~objek = b~objek
          INNER JOIN ausp AS c ON b~cuobj = c~objek
          INNER JOIN cabn AS d ON c~atinn = d~atinn
            WHERE b~klart = '022'
              AND b~obtab = 'MCHA'
              AND d~atnam = 'Z_BATCH_011'
              AND c~atwrt IN @s_batch
            INTO TABLE @DATA(lt_mcha2).
          " 匹配删除
          " 税率计算，由税码转换成税率
          DATA lv_datab1 TYPE a002-datab.
          lv_datab = sy-datum.
          SELECT b~mwsk1
            FROM a002 AS a
            INNER JOIN konp AS b ON a~knumh = b~knumh
            WHERE a~aland = 'CN'
              AND a~datbi >= @lv_datab
              AND a~taxk1 = '1'
            INTO @DATA(lv_mwskz3).
          ENDSELECT.
          " 税码
          IF sy-subrc = 0.
            gs_item-mwskz = lv_mwskz3.
          ENDIF.
  
  
          " 税率
          IF gs_item-mwskz IS NOT INITIAL .
            CALL FUNCTION 'GET_TAX_PERCENTAGE'
              EXPORTING
                aland   = 'CN'
                datab   = sy-datum
                mwskz   = gs_item-mwskz
                txjcd   = '1'
              TABLES
                t_ftaxp = lt_ftaxp.
            READ TABLE lt_ftaxp INTO DATA(ls_ftaxp3) WITH KEY kschl = 'MWVS'.
            IF sy-subrc = 0.
              gs_item-tax_rate = ls_ftaxp3-kbetr / 10.
              CLEAR:ls_ftaxp3,lt_ftaxp.
            ENDIF.
          ENDIF.
          " 结算单价
          gs_item-setl_price = gs_item-ord_price + gs_item-oth_price.
          " 含税金额计算
          gs_item-total_amount = gs_item-settle_qty * gs_item-setl_price.
          " 不含税金额计算
          gs_item-net_amount = gs_item-total_amount / ( 1 + gs_item-tax_rate ).
          " 税额计算
          gs_item-tax_amount = gs_item-total_amount - gs_item-net_amount.
  
          "输出alv界面
          APPEND ls_alv TO gt_alv.
          CLEAR ls_alv.
  
          " 回写表
          MOVE-CORRESPONDING ls_alv TO gs_item.
          " 结算单位
          gs_item-meins = ls_alv-unit.
          MODIFY gt_item FROM gs_item.
          CLEAR ls_cont.
        ENDLOOP.
  
  * 购销方向为销售 = ‘S’且结算类型 = ‘2’
      ELSEIF p_gxfx = 'S' AND p_stltyp = '2'.
        SELECT h~zzcont,
             h~partner,
             h~name1,
             h~class2_name,
             p~vbeln AS vbeln1,
             p~werks,
             p~matnr,
             p~meins,
             l~vbeln,
             l~posnr,
             l~lfimg,
  *             l~menge,
             l~charg
        FROM lips AS l
        INNER JOIN vbap AS p ON l~vgbel = p~vbeln
                            AND l~vgpos = p~posnr
        INNER JOIN vbak AS k ON p~vbeln = k~vbeln
        INNER JOIN vbkd AS d ON p~vbeln = d~vbeln
        INNER JOIN zmmt_jyys_head AS h ON d~bstkd = h~zzcont
        WHERE h~bukrs = @p_bukrs
          AND h~zzgxfx = @p_gxfx
          AND h~partner IN @s_patner
          AND p~vbeln IN @s_order
          AND h~zzcont IN @s_cont
          AND p~matnr IN @s_matnr
          AND d~posnr = ''
        INTO TABLE @DATA(lt_lips).
        PERFORM frm_get_domain_txt TABLES lt_dd07t_zstatus
                                   USING 'ZDZSTATUS'.
        SORT lt_dd07t_zstatus BY domvalue_l.
  
        " 数据整理
        LOOP AT lt_lips INTO DATA(ls_lips).
          ls_alv-zzcont = ls_lips-zzcont.
          ls_alv-buname = ls_lips-partner.
  *        ls_alv- = ls_lips-name1.
          ls_alv-maktx = ls_lips-class2_name.
          ls_alv-order_no = ls_lips-vbeln1.
          ls_alv-werks = ls_lips-werks.
  *        ls_alv- = ls_lips-matnr.
          ls_alv-meins = ls_lips-meins.
          ls_alv-vbeln = ls_lips-vbeln.
          ls_alv-posnr = ls_lips-posnr.
          ls_alv-lfimg = ls_lips-lfimg.
          ls_alv-z_batch_011 = ls_lips-charg.
          " 抬头数据
          READ TABLE gt_headdata INTO DATA(ls_head3) WITH KEY zzcont = ls_lips-zzcont.
          IF sy-subrc = 0.
            MOVE-CORRESPONDING ls_head3 TO ls_alv.
          ENDIF.
          " 物料编号
  *        ls_alv-matnr = ls_lips-matnr.
          " 物料描述
          IF ls_alv-maktx IS INITIAL.
            SELECT SINGLE maktx FROM makt
              WHERE matnr = @ls_lips-matnr
                AND spras = '1'
              INTO @ls_alv-maktx.
          ENDIF.
  *        ls_alv-maktx = ls_lips-maktx.
          " 合同编号
          ls_alv-zzcont = ls_lips-zzcont.
          " 合同吨数
          SELECT SUM( b~zzkwmeng ) AS zzkwmeng1,
                 b~zzcont
            FROM zmmt_jyys_item AS b
            WHERE b~zzcont = @ls_lips-zzcont
             GROUP BY b~zzcont
             INTO @DATA(lv_zzkwmeng3).
          ENDSELECT.
  
          ls_alv-zzkwmeng = lv_zzkwmeng3-zzkwmeng1.
          " 已结算数量，结算单位
          SELECT SINGLE
                 b~settle_qty,
                 a~meins
            FROM zmmt_settle_head AS a
            INNER JOIN zmmt_settle_item AS b ON a~zsqid = b~zsqid
            WHERE a~zzcont = @ls_lips-zzcont
              AND a~zstatus <> '05'
              AND a~zstatus <> '40'
             INTO @DATA(lv_qty3).
  
          ls_alv-sqty1 = lv_qty3-settle_qty.
          ls_alv-meins = lv_qty3-meins.
          " 重量单位
          SELECT matnr,
                 gewei
            FROM mara
            WHERE matnr = @ls_lips-matnr
            INTO @DATA(lv_gewei3).
          ENDSELECT.
  
          ls_alv-gewei = lv_gewei3-gewei.
          ls_alv-unit = lv_gewei3-gewei.
          " 可结算数量
          " 单位换算
          IF ls_alv-zzkwmeng IS NOT INITIAL AND ls_alv-menge IS NOT INITIAL.
            IF ls_alv-gewei = 'KG'.
              ls_alv-menge = ls_alv-zzkwmeng * 1000.
              ls_alv-meins = 1000.
            ELSEIF ls_alv-gewei = 'A75' OR ls_alv-gewei = '百克' .
              ls_alv-menge = ls_alv-zzkwmeng * 10000.
              ls_alv-meins = 10000.
            ELSE.
              ls_alv-meins = 1.
              ls_alv-menge = ls_alv-zzkwmeng.
            ENDIF.
          ELSEIF ls_alv-menge IS NOT INITIAL AND ls_alv-zzkwmeng IS INITIAL.
            IF ls_alv-gewei = 'TO'.
              ls_alv-zzkwmeng = ls_alv-menge.
            ELSEIF ls_alv-gewei = 'KG'.
              ls_alv-zzkwmeng = ls_alv-menge / 1000.
            ELSEIF ls_alv-gewei = 'A75' OR ls_alv-gewei = '百克' .
              ls_alv-zzkwmeng = ls_alv-menge / 10000.
            ENDIF.
          ENDIF.
          " 可结算数量
          ls_alv-sqty2 = ls_alv-menge - ls_alv-sqty1.
  
          "输出alv界面
          APPEND ls_alv TO gt_alv.
          " 回写表
          MOVE-CORRESPONDING ls_alv TO gs_item.
          gs_item-meins = ls_alv-unit.
          MODIFY gt_item FROM gs_item.
          CLEAR ls_lips.
          CLEAR ls_alv.
        ENDLOOP.
      ENDIF.
    ENDIF.
  
    IF gt_alv IS NOT INITIAL.
    ELSE.
      MESSAGE '查询条件无数据' TYPE 'S' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ENDIF.
  
  ENDFORM.
  *&---------------------------------------------------------------------*
  *& Form FRM_PROCESS_DATA
  *&---------------------------------------------------------------------*
  *& 处理数据
  *&---------------------------------------------------------------------*
  *& -->  p1        text
  *& <--  p2        text
  *&---------------------------------------------------------------------*
  FORM frm_process_data.
    DATA: lv_partner TYPE but000-partner.
  *  DATA: lt_partner TYPE TABLE OF ty_partner.
  
  
    " 必输校验
    " 公司代码全程必输
    IF p_bukrs IS INITIAL .
      MESSAGE '公司代码必输' TYPE 'S' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING .
    ENDIF.
    " 购销方向全程必输
    IF p_gxfx IS INITIAL.
      MESSAGE '购销方向必输' TYPE 'S' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING .
    ENDIF.
    " 结算类型全程必输
    IF p_stltyp IS INITIAL.
      MESSAGE '结算类型必输' TYPE 'S' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING .
    ENDIF.
  
    IF p_pcjs IS INITIAL.
      IF s_patner AND s_cont AND s_order AND s_matnr IS INITIAL.
        MESSAGE '客商编码，合同编号，订单编号，物料编号请至少输入一项' TYPE 'S' DISPLAY LIKE 'E'.
        LEAVE LIST-PROCESSING .
      ENDIF.
    ELSE.
      IF p_gxfx = 'B'.
        IF s_patner AND s_cont AND s_order AND s_matnr AND s_grdate AND s_batch IS INITIAL.
          MESSAGE '客商编码，合同编号，订单编号，物料编号，到货批次，登记日期请至少输入一项' TYPE 'S' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING .
        ENDIF.
      ENDIF.
      IF p_gxfx = 'S' AND ( p_stltyp = '0' OR  p_stltyp = '1' ).
        " 合同编号必输
        IF s_cont IS INITIAL.
          MESSAGE '合同编号必输' TYPE 'S' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING .
        ENDIF.
        " 工厂必输
        IF p_werks IS INITIAL.
          MESSAGE '工厂必输' TYPE 'S' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING .
        ENDIF.
      ENDIF.
      IF p_gxfx = 'S' AND p_stltyp = '2'.
        IF s_patner AND s_cont AND s_order AND s_matnr AND s_grdate AND s_batch IS INITIAL.
          MESSAGE '客商编码，合同编号，订单编号，物料编号，到货批次，登记日期请至少输入一项' TYPE 'S' DISPLAY LIKE 'E'.
          LEAVE LIST-PROCESSING .
        ENDIF.
      ENDIF.
    ENDIF.
  
    " 结算申请必输
    IF rb_chg = 'X'.
      MESSAGE '结算申请号必输' TYPE 'S' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING .
    ENDIF.
  
    " 结算类型下拉控制
    DEFINE fill_list.
      ls_stylp-key  = &1.
      ls_stylp-text = &2.
      APPEND ls_stylp TO list.
    END-OF-DEFINITION.
  
  
    "抬头数据初始化
    gs_head-zsqid   = p_zsqid.
    gs_head-bukrs   = p_bukrs.
    gs_head-zzgxfx  = p_gxfx.
    gs_head-partner = s_patner.
    gs_head-zzcont  = s_cont.
    CALL FUNCTION 'CONVERSION_EXIT_ALPHA_INPUT'
      EXPORTING
        input  = gs_head-partner
      IMPORTING
        output = lv_partner.
  
    " 提单日期
    gs_head-vrzdt = sy-datum.
    " 提单人
    SELECT SINGLE aduser FROM  zfit_user_domain
    INTO gs_head-zuser2
    WHERE uname = sy-uname.
    "申请人
    gs_head-zuser1 = gs_head-zuser2.
  
    " 客商名称
  *  SELECT SINGLE mc_name1 FROM but000
  *    WHERE partner EQ @lv_partner
  *    INTO @gs_head-buname.
    " 货币码
    SELECT SINGLE bu_group FROM but000
    WHERE partner EQ @lv_partner
    INTO @DATA(lv_group).
    IF sy-subrc =  0.
      IF lv_group = 'Z002'."供应商
        SELECT SINGLE land1 FROM lfa1
        WHERE lifnr = @lv_partner
        INTO @DATA(lv_land1).
      ELSEIF lv_group = 'Z001' ."客户
        SELECT SINGLE land1 FROM kna1
        WHERE kunnr = @lv_partner
        INTO @lv_land1.
      ENDIF .
      IF lv_land1 = 'CN'.
        gs_head-waers =  'CNY'.
      ELSE.
        gs_head-waers =  'USD'.
      ENDIF.
    ENDIF.
    " 品类描述
    SELECT SINGLE class2_name FROM zmmt_maclass
    WHERE class2 EQ @gs_head-class_name
    AND  org_id = 'C01.01'
    INTO @gs_head-class_name.
  
  
  ENDFORM.
  *&---------------------------------------------------------------------*
  *& Form FRM_AUTHORITY_CHECK
  *&---------------------------------------------------------------------*
  *& 权限检查
  *&---------------------------------------------------------------------*
  *& -->  p1        text
  *& <--  p2        text
  *&---------------------------------------------------------------------*
  FORM frm_authority_check.
  
    " 公司代码维护权限检查
    AUTHORITY-CHECK OBJECT 'M_RECH_BUK'
      ID 'BUKRS' FIELD p_bukrs
      ID 'ACTVT' FIELD '01'.
    IF sy-subrc <> 0.
      MESSAGE s000 WITH '没有公司代码' p_bukrs '的权限' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING.
    ENDIF.
  
  ENDFORM.
  *&---------------------------------------------------------------------*
  *& FORM FRM_VALIDATE_CHECK
  *&---------------------------------------------------------------------*
  *& 数据合法性检查
  *&---------------------------------------------------------------------*
  *& -->  p1        text
  *& <--  p2        text
  *&---------------------------------------------------------------------*
  *FORM frm_validate_check .
  
  *ENDFORM.
  *&---------------------------------------------------------------------*
  *& Form FRM_SAVE_DATA
  *&---------------------------------------------------------------------*
  *& 保存数据
  *&---------------------------------------------------------------------*
  *& -->  p1        text
  *& <--  p2        text
  *&---------------------------------------------------------------------*
  *FORM frm_save_data .
  
  *ENDFORM.
  *&---------------------------------------------------------------------*
  *& Form FRM_CLEAR_GLOBAL_DATA
  *&---------------------------------------------------------------------*
  *& 清空所有全局变量
  *&---------------------------------------------------------------------*
  *& -->  p1        text
  *& <--  p2        text
  *&---------------------------------------------------------------------*
  *FORM frm_clear_global_data .
  
  *ENDFORM.
  *&---------------------------------------------------------------------*
  *& Form FRM_CALL_SCREEN
  *&---------------------------------------------------------------------*
  *& 调用屏幕
  *&---------------------------------------------------------------------*
  *& -->  p1        text
  *& <--  p2        text
  *&---------------------------------------------------------------------*
  *FORM frm_call_screen .
  
  *ENDFORM.
  *&---------------------------------------------------------------------*
  *& Form FRM_INTIAL
  *&---------------------------------------------------------------------*
  *& 初始化处理
  *&---------------------------------------------------------------------*
  *& -->  p1        text
  *& <--  p2        text
  *&---------------------------------------------------------------------*
  *FORM frm_intial .
  
  *ENDFORM.
  *&---------------------------------------------------------------------*
  *&      Form  FRM_SET_FIELDCAT
  *&---------------------------------------------------------------------*
  *       text
  *----------------------------------------------------------------------*
  *      -->P_0266   text
  *      -->P_0267   text
  *      -->P_0268   text
  *      -->P_0269   text
  *      -->P_0270   text
  *----------------------------------------------------------------------*
  FORM frm_set_fieldcat  USING p_field p_name p_edit p_rtable p_rfield.
    gs_fieldcat-col_pos   = gv_col.
    gs_fieldcat-fieldname = p_field.
    gs_fieldcat-coltext   = p_name.
    gs_fieldcat-scrtext_l = p_name.
    gs_fieldcat-scrtext_m = p_name.
    gs_fieldcat-scrtext_s = p_name.
    gs_fieldcat-ref_table = p_rtable.
    gs_fieldcat-ref_field = p_rfield.
    gs_fieldcat-edit      = p_edit.
    APPEND gs_fieldcat TO gt_fieldcat.
    gv_col = gv_col + 1.
    CLEAR gs_fieldcat.
  ENDFORM.
  *&---------------------------------------------------------------------*
  *& Form FRM_DISPLAY_ALV
  *&---------------------------------------------------------------------*
  *& text
  *&---------------------------------------------------------------------*
  *& -->  p1        text
  *& <--  p2        text
  *&---------------------------------------------------------------------*
  FORM frm_display_alv .
    PERFORM frm_init_fieldcat.
    PERFORM frm_init_layout.
    PERFORM frm_init_alv.
  ENDFORM.
  *&---------------------------------------------------------------------*
  *& Form FRM_INIT_FIELDCAT
  *&---------------------------------------------------------------------*
  *& text
  *&---------------------------------------------------------------------*
  *& -->  p1        text
  *& <--  p2        text
  *&---------------------------------------------------------------------*
  FORM frm_init_fieldcat .
    REFRESH:gt_fieldcat.
    IF rb_crt = 'X'.
      IF p_gxfx = 'B'AND p_pcjs IS INITIAL.
        PERFORM frm_set_fieldcat USING 'ZZCONT' '合同编号'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZZCONT' .
        PERFORM frm_set_fieldcat USING 'BUNAME' '客商名称'(t02) '' 'ZMMT_SETTLE_HEAD' 'BUNAME' .
        PERFORM frm_set_fieldcat USING 'ORDER_NO' '订单编号'(t03) '' 'ZMMT_SETTLE_ITEM' 'ORDER_NO' .
        PERFORM frm_set_fieldcat USING 'ORDER_ITEM' '订单项目'(t04) '' 'ZMMT_SETTLE_ITEM' 'ORDER_ITEM' .
        PERFORM frm_set_fieldcat USING 'WERKS' '工厂'(t05) '' 'ZMMT_SETTLE_ITEM' 'WERKS' .
        PERFORM frm_set_fieldcat USING 'MAKTX' '品名'(t06) '' 'ZMMT_SETTLE_HEAD' 'CLASS_NAME' .
        PERFORM frm_set_fieldcat USING 'ZZKWMENG' '合同吨数'(t07) '' '' '' .
        PERFORM frm_set_fieldcat USING 'MENGE' '订单数量'(t08) '' 'ZMMT_SETTLE_ITEM' 'MENGE' .
        PERFORM frm_set_fieldcat USING 'UNIT' '重量单位'(t09) '' '' '' .
        PERFORM frm_set_fieldcat USING 'SQTY1' '已结算数量'(t10) '' 'ZMMT_SETTLE_ITEM' 'SETTLE_QTY' .
        PERFORM frm_set_fieldcat USING 'SQTY2' '可结算数量'(t11) '' 'ZMMT_SETTLE_ITEM' 'SETTLE_QTY' .
      ELSEIF p_gxfx = 'B' AND p_pcjs IS NOT INITIAL.
        PERFORM frm_set_fieldcat USING 'ZZCONT' '合同编号'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZZCONT' .
        PERFORM frm_set_fieldcat USING 'BUNAME' '客商名称'(t02) '' 'ZMMT_SETTLE_HEAD' 'BUNAME' .
        PERFORM frm_set_fieldcat USING 'ORDER_NO' '订单编号'(t03) '' 'ZMMT_SETTLE_ITEM' 'ORDER_NO' .
        PERFORM frm_set_fieldcat USING 'ORDER_ITEM' '订单项目'(t04) '' 'ZMMT_SETTLE_ITEM' 'ORDER_ITEM' .
        PERFORM frm_set_fieldcat USING 'WERKS' '工厂'(t05) '' 'ZMMT_SETTLE_ITEM' 'WERKS' .
        PERFORM frm_set_fieldcat USING 'MAKTX' '品名'(t06) '' 'ZMMT_SETTLE_HEAD' 'CLASS_NAME' .
        PERFORM frm_set_fieldcat USING 'VBELN' '交货单'(t01) '' 'ZMMT_SETTLE_ITEM' 'VBELN' .
        PERFORM frm_set_fieldcat USING 'POSNR' '交货项目'(t01) '' 'ZMMT_SETTLE_ITEM' 'POSNR' .
        PERFORM frm_set_fieldcat USING 'CHARG' 'SAP批次'(t01) '' 'ZMMT_SETTLE_ITEM' 'CHARG' .
        PERFORM frm_set_fieldcat USING 'Z_BATCH_011' '到货批次'(t01) '' 'ZMMT_SETTLE_ITEM' 'Z_BATCH_011' .
        PERFORM frm_set_fieldcat USING 'UNIT' '重量单位'(t01) '' '' '' .
      ELSEIF p_gxfx = 'S'AND p_pcjs IS INITIAL.
        PERFORM frm_set_fieldcat USING 'ZZCONT' '合同编号'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZZCONT' .
        PERFORM frm_set_fieldcat USING 'BUNAME' '客商名称'(t02) '' 'ZMMT_SETTLE_HEAD' 'BUNAME' .
        PERFORM frm_set_fieldcat USING 'ORDER_NO' '订单编号'(t03) '' 'ZMMT_SETTLE_ITEM' 'ORDER_NO' .
        PERFORM frm_set_fieldcat USING 'ORDER_ITEM' '订单项目'(t04) '' 'ZMMT_SETTLE_ITEM' 'ORDER_ITEM' .
        PERFORM frm_set_fieldcat USING 'WERKS' '工厂'(t05) '' 'ZMMT_SETTLE_ITEM' 'WERKS' .
        PERFORM frm_set_fieldcat USING 'MAKTX' '品名'(t06) '' 'ZMMT_SETTLE_HEAD' 'CLASS_NAME' .
        PERFORM frm_set_fieldcat USING 'ZZKWMENG' '合同吨数'(t07) '' '' '' .
        PERFORM frm_set_fieldcat USING 'MENGE' '订单数量'(t08) '' 'ZMMT_SETTLE_ITEM' 'MENGE' .
        PERFORM frm_set_fieldcat USING 'UNIT' '重量单位'(t09) '' '' '' .
        PERFORM frm_set_fieldcat USING 'SQTY1' '已结算数量'(t10) '' 'ZMMT_SETTLE_ITEM' 'SETTLE_QTY' .
        PERFORM frm_set_fieldcat USING 'SQTY2' '可结算数量'(t11) '' 'ZMMT_SETTLE_ITEM' 'SETTLE_QTY' .
      ELSEIF p_gxfx = 'S'AND p_pcjs = 'X'AND ( p_stltyp = '1' OR p_stltyp = '0') .
        PERFORM frm_set_fieldcat USING 'ZZCONT' '合同编号'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZZCONT' .
        PERFORM frm_set_fieldcat USING 'BUNAME' '客商名称'(t02) '' 'ZMMT_SETTLE_HEAD' 'BUNAME' .
        PERFORM frm_set_fieldcat USING 'ORDER_NO' '订单编号'(t03) '' 'ZMMT_SETTLE_ITEM' 'ORDER_NO' .
        PERFORM frm_set_fieldcat USING 'ORDER_ITEM' '订单项目'(t04) '' 'ZMMT_SETTLE_ITEM' 'ORDER_ITEM' .
        PERFORM frm_set_fieldcat USING 'WERKS' '工厂'(t05) '' 'ZMMT_SETTLE_ITEM' 'WERKS' .
        PERFORM frm_set_fieldcat USING 'MAKTX' '品名'(t06) '' 'ZMMT_SETTLE_HEAD' 'CLASS_NAME' .
        PERFORM frm_set_fieldcat USING 'ZZKWMENG' '合同吨数'(t07) '' '' '' .
        PERFORM frm_set_fieldcat USING 'MENGE' '订单数量'(t08) '' 'ZMMT_SETTLE_ITEM' 'MENGE' .
        PERFORM frm_set_fieldcat USING 'UNIT' '重量单位'(t09) '' '' '' .
        PERFORM frm_set_fieldcat USING 'SQTY1' '已结算数量'(t10) '' 'ZMMT_SETTLE_ITEM' 'SETTLE_QTY' .
        PERFORM frm_set_fieldcat USING 'SQTY2' '可结算数量'(t11) '' 'ZMMT_SETTLE_ITEM' 'SETTLE_QTY' .
      ELSEIF p_gxfx = 'S'AND p_pcjs = 'X'AND p_stltyp = '2' .
        PERFORM frm_set_fieldcat USING 'ZZCONT' '合同编号'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZZCONT' .
        PERFORM frm_set_fieldcat USING 'BUNAME' '客商名称'(t02) '' 'BUT000' 'MC_NAME1' .
        PERFORM frm_set_fieldcat USING 'ORDER_NO' '订单编号'(t03) '' 'ZMMT_SETTLE_ITEM' 'ORDER_NO' .
        PERFORM frm_set_fieldcat USING 'ORDER_ITEM' '订单项目'(t04) '' 'ZMMT_SETTLE_ITEM' 'ORDER_ITEM' .
        PERFORM frm_set_fieldcat USING 'WERKS' '工厂'(t05) '' 'ZMMT_SETTLE_ITEM' 'WERKS' .
        PERFORM frm_set_fieldcat USING 'MAKTX' '品名'(t06) '' 'ZMMT_SETTLE_HEAD' 'CLASS_NAME' .
        PERFORM frm_set_fieldcat USING 'VBELN' '交货单'(t01) '' 'ZMMT_SETTLE_ITEM' 'VBELN' .
        PERFORM frm_set_fieldcat USING 'POSNR' '交货项目'(t01) '' 'ZMMT_SETTLE_ITEM' 'POSNR' .
        PERFORM frm_set_fieldcat USING 'CHARG' 'SAP批次'(t01) '' 'ZMMT_SETTLE_ITEM' 'CHARG' .
        PERFORM frm_set_fieldcat USING 'Z_BATCH_011' '到货批次'(t01) '' 'ZMMT_SETTLE_ITEM' 'Z_BATCH_011' .
        PERFORM frm_set_fieldcat USING 'UNIT' '重量单位'(t01) '' '' '' .
      ENDIF.
    ELSEIF rb_chck = 'X'.
      IF p_gxfx = 'B'AND p_pcjs IS INITIAL.
        PERFORM frm_set_fieldcat USING 'ZSQID' '结算申请'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZSQID' .
        PERFORM frm_set_fieldcat USING 'ZSTATUS' '状态'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZSTATUS' .
        PERFORM frm_set_fieldcat USING 'K2ID' 'K2唯一ID'(t01) '' 'ZMMT_SETTLE_HEAD' 'K2ID' .
        PERFORM frm_set_fieldcat USING 'ZZCONT' '合同编号'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZZCONT' .
        PERFORM frm_set_fieldcat USING 'BUNAME' '客商名称'(t02) '' 'ZMMT_SETTLE_HEAD' 'BUNAME' .
        PERFORM frm_set_fieldcat USING 'ORDER_NO' '订单编号'(t03) '' 'ZMMT_SETTLE_ITEM' 'ORDER_NO' .
        PERFORM frm_set_fieldcat USING 'ORDER_ITEM' '订单项目'(t04) '' 'ZMMT_SETTLE_ITEM' 'ORDER_ITEM' .
        PERFORM frm_set_fieldcat USING 'WERKS' '工厂'(t05) '' 'ZMMT_SETTLE_ITEM' 'WERKS' .
        PERFORM frm_set_fieldcat USING 'MAKTX' '品名'(t06) '' 'ZMMT_SETTLE_HEAD' 'CLASS_NAME' .
        PERFORM frm_set_fieldcat USING 'ZZKWMENG' '合同吨数'(t07) '' '' '' .
        PERFORM frm_set_fieldcat USING 'MENGE' '订单数量'(t08) '' 'ZMMT_SETTLE_ITEM' 'MENGE' .
        PERFORM frm_set_fieldcat USING 'UNIT' '重量单位'(t09) '' '' '' .
        PERFORM frm_set_fieldcat USING 'SQTY1' '已结算数量'(t10) '' 'ZMMT_SETTLE_ITEM' 'SETTLE_QTY' .
        PERFORM frm_set_fieldcat USING 'SQTY2' '可结算数量'(t11) '' 'ZMMT_SETTLE_ITEM' 'SETTLE_QTY' .
        PERFORM frm_set_fieldcat USING 'ZCNAM' '创建人'(t12) '' 'ZMMT_SETTLE_HEAD' 'ZCNAM' .
        PERFORM frm_set_fieldcat USING 'ZCDAT' '创建日期'(t13) '' 'ZMMT_SETTLE_HEAD' 'ZCDAT' .
        PERFORM frm_set_fieldcat USING 'ZCTIM' '创建时间'(t14) '' 'ZMMT_SETTLE_HEAD' 'ZCTIM' .
        PERFORM frm_set_fieldcat USING 'ZUNAM' '最后修改人'(t15) '' 'ZMMT_SETTLE_HEAD' 'ZUNAM' .
        PERFORM frm_set_fieldcat USING 'ZUDAT' '更改日期'(t16) '' 'ZMMT_SETTLE_HEAD' 'ZUDAT' .
        PERFORM frm_set_fieldcat USING 'ZUTIM' '更改时间'(t17) '' 'ZMMT_SETTLE_HEAD' 'ZUTIM' .
      ELSEIF p_gxfx = 'B' AND p_pcjs IS NOT INITIAL.
        PERFORM frm_set_fieldcat USING 'ZSQID' '结算申请'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZSQID' .
        PERFORM frm_set_fieldcat USING 'ZSTATUS' '状态'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZSTATUS' .
        PERFORM frm_set_fieldcat USING 'K2ID' 'K2唯一ID'(t01) '' 'ZMMT_SETTLE_HEAD' 'K2ID' .
        PERFORM frm_set_fieldcat USING 'ZZCONT' '合同编号'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZZCONT' .
        PERFORM frm_set_fieldcat USING 'BUNAME' '客商名称'(t02) '' 'ZMMT_SETTLE_HEAD' 'BUNAME' .
        PERFORM frm_set_fieldcat USING 'ORDER_NO' '订单编号'(t03) '' 'ZMMT_SETTLE_ITEM' 'ORDER_NO' .
        PERFORM frm_set_fieldcat USING 'ORDER_ITEM' '订单项目'(t04) '' 'ZMMT_SETTLE_ITEM' 'ORDER_ITEM' .
        PERFORM frm_set_fieldcat USING 'WERKS' '工厂'(t05) '' 'ZMMT_SETTLE_ITEM' 'WERKS' .
        PERFORM frm_set_fieldcat USING 'MAKTX' '品名'(t06) '' 'ZMMT_SETTLE_HEAD' 'CLASS_NAME' .
        PERFORM frm_set_fieldcat USING 'VBELN' '交货单'(t01) '' 'ZMMT_SETTLE_ITEM' 'VBELN' .
        PERFORM frm_set_fieldcat USING 'POSNR' '交货项目'(t01) '' 'ZMMT_SETTLE_ITEM' 'POSNR' .
        PERFORM frm_set_fieldcat USING 'CHARG' 'SAP批次'(t01) '' 'ZMMT_SETTLE_ITEM' 'CHARG' .
        PERFORM frm_set_fieldcat USING 'Z_BATCH_011' '到货批次'(t01) '' 'ZMMT_SETTLE_ITEM' 'Z_BATCH_011' .
        PERFORM frm_set_fieldcat USING 'UNIT' '重量单位'(t01) '' '' '' .
        PERFORM frm_set_fieldcat USING 'ZCNAM' '创建人'(t12) '' 'ZMMT_SETTLE_HEAD' 'ZCNAM' .
        PERFORM frm_set_fieldcat USING 'ZCDAT' '创建日期'(t13) '' 'ZMMT_SETTLE_HEAD' 'ZCDAT' .
        PERFORM frm_set_fieldcat USING 'ZCTIM' '创建时间'(t14) '' 'ZMMT_SETTLE_HEAD' 'ZCTIM' .
        PERFORM frm_set_fieldcat USING 'ZUNAM' '最后修改人'(t15) '' 'ZMMT_SETTLE_HEAD' 'ZUNAM' .
        PERFORM frm_set_fieldcat USING 'ZUDAT' '更改日期'(t16) '' 'ZMMT_SETTLE_HEAD' 'ZUDAT' .
        PERFORM frm_set_fieldcat USING 'ZUTIM' '更改时间'(t17) '' 'ZMMT_SETTLE_HEAD' 'ZUTIM' .
      ELSEIF p_gxfx = 'S'AND p_pcjs IS INITIAL.
        PERFORM frm_set_fieldcat USING 'ZSQID' '结算申请'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZSQID' .
        PERFORM frm_set_fieldcat USING 'ZSTATUS' '状态'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZSTATUS' .
        PERFORM frm_set_fieldcat USING 'K2ID' 'K2唯一ID'(t01) '' 'ZMMT_SETTLE_HEAD' 'K2ID' .
        PERFORM frm_set_fieldcat USING 'ZZCONT' '合同编号'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZZCONT' .
        PERFORM frm_set_fieldcat USING 'BUNAME' '客商名称'(t02) '' 'ZMMT_SETTLE_HEAD' 'BUNAME' .
        PERFORM frm_set_fieldcat USING 'ORDER_NO' '订单编号'(t03) '' 'ZMMT_SETTLE_ITEM' 'ORDER_NO' .
        PERFORM frm_set_fieldcat USING 'ORDER_ITEM' '订单项目'(t04) '' 'ZMMT_SETTLE_ITEM' 'ORDER_ITEM' .
        PERFORM frm_set_fieldcat USING 'WERKS' '工厂'(t05) '' 'ZMMT_SETTLE_ITEM' 'WERKS' .
        PERFORM frm_set_fieldcat USING 'MAKTX' '品名'(t06) '' 'ZMMT_SETTLE_HEAD' 'CLASS_NAME' .
        PERFORM frm_set_fieldcat USING 'VBELN' '交货单'(t01) '' 'ZMMT_SETTLE_ITEM' 'VBELN' .
        PERFORM frm_set_fieldcat USING 'POSNR' '交货项目'(t01) '' 'ZMMT_SETTLE_ITEM' 'POSNR' .
        PERFORM frm_set_fieldcat USING 'CHARG' 'SAP批次'(t01) '' 'ZMMT_SETTLE_ITEM' 'CHARG' .
        PERFORM frm_set_fieldcat USING 'Z_BATCH_011' '到货批次'(t01) '' 'ZMMT_SETTLE_ITEM' 'Z_BATCH_011' .
        PERFORM frm_set_fieldcat USING 'UNIT' '重量单位'(t01) '' '' '' .
        PERFORM frm_set_fieldcat USING 'ZCNAM' '创建人'(t12) '' 'ZMMT_SETTLE_HEAD' 'ZCNAM' .
        PERFORM frm_set_fieldcat USING 'ZCDAT' '创建日期'(t13) '' 'ZMMT_SETTLE_HEAD' 'ZCDAT' .
        PERFORM frm_set_fieldcat USING 'ZCTIM' '创建时间'(t14) '' 'ZMMT_SETTLE_HEAD' 'ZCTIM' .
        PERFORM frm_set_fieldcat USING 'ZUNAM' '最后修改人'(t15) '' 'ZMMT_SETTLE_HEAD' 'ZUNAM' .
        PERFORM frm_set_fieldcat USING 'ZUDAT' '更改日期'(t16) '' 'ZMMT_SETTLE_HEAD' 'ZUDAT' .
        PERFORM frm_set_fieldcat USING 'ZUTIM' '更改时间'(t17) '' 'ZMMT_SETTLE_HEAD' 'ZUTIM' .
      ELSEIF p_gxfx = 'S'AND p_pcjs = 'X'AND ( p_stltyp = '1' OR p_stltyp = '0') .
        PERFORM frm_set_fieldcat USING 'ZSQID' '结算申请'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZSQID' .
        PERFORM frm_set_fieldcat USING 'ZSTATUS' '状态'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZSTATUS' .
        PERFORM frm_set_fieldcat USING 'K2ID' 'K2唯一ID'(t01) '' 'ZMMT_SETTLE_HEAD' 'K2ID' .
        PERFORM frm_set_fieldcat USING 'ZZCONT' '合同编号'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZZCONT' .
        PERFORM frm_set_fieldcat USING 'BUNAME' '客商名称'(t02) '' 'ZMMT_SETTLE_HEAD' 'BUNAME' .
        PERFORM frm_set_fieldcat USING 'ORDER_NO' '订单编号'(t03) '' 'ZMMT_SETTLE_ITEM' 'ORDER_NO' .
        PERFORM frm_set_fieldcat USING 'ORDER_ITEM' '订单项目'(t04) '' 'ZMMT_SETTLE_ITEM' 'ORDER_ITEM' .
        PERFORM frm_set_fieldcat USING 'WERKS' '工厂'(t05) '' 'ZMMT_SETTLE_ITEM' 'WERKS' .
        PERFORM frm_set_fieldcat USING 'MAKTX' '品名'(t06) '' 'ZMMT_SETTLE_HEAD' 'CLASS_NAME' .
        PERFORM frm_set_fieldcat USING 'ZZKWMENG' '合同吨数'(t07) '' '' '' .
        PERFORM frm_set_fieldcat USING 'MENGE' '订单数量'(t08) '' 'ZMMT_SETTLE_ITEM' 'MENGE' .
        PERFORM frm_set_fieldcat USING 'UNIT' '重量单位'(t09) '' '' '' .
        PERFORM frm_set_fieldcat USING 'SQTY1' '已结算数量'(t10) '' 'ZMMT_SETTLE_ITEM' 'SETTLE_QTY' .
        PERFORM frm_set_fieldcat USING 'SQTY2' '可结算数量'(t11) '' 'ZMMT_SETTLE_ITEM' 'SETTLE_QTY' .
        PERFORM frm_set_fieldcat USING 'ZCNAM' '创建人'(t12) '' 'ZMMT_SETTLE_HEAD' 'ZCNAM' .
        PERFORM frm_set_fieldcat USING 'ZCDAT' '创建日期'(t13) '' 'ZMMT_SETTLE_HEAD' 'ZCDAT' .
        PERFORM frm_set_fieldcat USING 'ZCTIM' '创建时间'(t14) '' 'ZMMT_SETTLE_HEAD' 'ZCTIM' .
        PERFORM frm_set_fieldcat USING 'ZUNAM' '最后修改人'(t15) '' 'ZMMT_SETTLE_HEAD' 'ZUNAM' .
        PERFORM frm_set_fieldcat USING 'ZUDAT' '更改日期'(t16) '' 'ZMMT_SETTLE_HEAD' 'ZUDAT' .
        PERFORM frm_set_fieldcat USING 'ZUTIM' '更改时间'(t17) '' 'ZMMT_SETTLE_HEAD' 'ZUTIM' .
      ELSEIF p_gxfx = 'S'AND p_pcjs = 'X'AND p_stltyp = '2' .
        PERFORM frm_set_fieldcat USING 'ZSQID' '结算申请'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZSQID' .
        PERFORM frm_set_fieldcat USING 'ZSTATUS' '状态'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZSTATUS' .
        PERFORM frm_set_fieldcat USING 'K2ID' 'K2唯一ID'(t01) '' 'ZMMT_SETTLE_HEAD' 'K2ID' .
        PERFORM frm_set_fieldcat USING 'ZZCONT' '合同编号'(t01) '' 'ZMMT_SETTLE_HEAD' 'ZZCONT' .
        PERFORM frm_set_fieldcat USING 'BUNAME' '客商名称'(t02) '' 'ZMMT_SETTLE_HEAD' 'BUNAME' .
        PERFORM frm_set_fieldcat USING 'ORDER_NO' '订单编号'(t03) '' 'ZMMT_SETTLE_ITEM' 'ORDER_NO' .
        PERFORM frm_set_fieldcat USING 'ORDER_ITEM' '订单项目'(t04) '' 'ZMMT_SETTLE_ITEM' 'ORDER_ITEM' .
        PERFORM frm_set_fieldcat USING 'WERKS' '工厂'(t05) '' 'ZMMT_SETTLE_ITEM' 'WERKS' .
        PERFORM frm_set_fieldcat USING 'MAKTX' '品名'(t06) '' 'ZMMT_SETTLE_HEAD' 'CLASS_NAME' .
        PERFORM frm_set_fieldcat USING 'VBELN' '交货单'(t01) '' 'ZMMT_SETTLE_ITEM' 'VBELN' .
        PERFORM frm_set_fieldcat USING 'POSNR' '交货项目'(t01) '' 'ZMMT_SETTLE_ITEM' 'POSNR' .
        PERFORM frm_set_fieldcat USING 'CHARG' 'SAP批次'(t01) '' 'ZMMT_SETTLE_ITEM' 'CHARG' .
        PERFORM frm_set_fieldcat USING 'Z_BATCH_011' '到货批次'(t01) '' 'ZMMT_SETTLE_ITEM' 'Z_BATCH_011' .
        PERFORM frm_set_fieldcat USING 'UNIT' '重量单位'(t01) '' '' '' .
        PERFORM frm_set_fieldcat USING 'ZCNAM' '创建人'(t12) '' 'ZMMT_SETTLE_HEAD' 'ZCNAM' .
        PERFORM frm_set_fieldcat USING 'ZCDAT' '创建日期'(t13) '' 'ZMMT_SETTLE_HEAD' 'ZCDAT' .
        PERFORM frm_set_fieldcat USING 'ZCTIM' '创建时间'(t14) '' 'ZMMT_SETTLE_HEAD' 'ZCTIM' .
        PERFORM frm_set_fieldcat USING 'ZUNAM' '最后修改人'(t15) '' 'ZMMT_SETTLE_HEAD' 'ZUNAM' .
        PERFORM frm_set_fieldcat USING 'ZUDAT' '更改日期'(t16) '' 'ZMMT_SETTLE_HEAD' 'ZUDAT' .
        PERFORM frm_set_fieldcat USING 'ZUTIM' '更改时间'(t17) '' 'ZMMT_SETTLE_HEAD' 'ZUTIM' .
      ENDIF.
    ENDIF.
  
  ENDFORM.
  *&---------------------------------------------------------------------*
  *& Form FRM_INIT_LAYOUT
  *&---------------------------------------------------------------------*
  *& text
  *&---------------------------------------------------------------------*
  *& -->  p1        text
  *& <--  p2        text
  *&---------------------------------------------------------------------*
  FORM frm_init_layout .
    gs_layout-sel_mode   = 'A' .
    gs_layout-cwidth_opt = 'X'.
    gs_layout-detailinit = 'X' .
    gs_layout-box_fname  = 'BOX'.
    gs_layout-stylefname = 'STYLE'.
  ENDFORM.
  *&---------------------------------------------------------------------*
  *& Form FRM_INIT_ALV
  *&---------------------------------------------------------------------*
  *& text
  *&---------------------------------------------------------------------*
  *& -->  p1        text
  *& <--  p2        text
  *&---------------------------------------------------------------------*
  FORM frm_init_alv .
    DATA :ls_grid_settings TYPE lvc_s_glay .
    ls_grid_settings-edt_cll_cb = 'X'.
  
    CALL FUNCTION 'REUSE_ALV_GRID_DISPLAY_LVC'
      EXPORTING
        i_callback_program       = sy-repid
        i_callback_pf_status_set = 'FRM_SET_PF_STATUS'
        i_callback_user_command  = 'FRM_USER_COMMAND'
        is_layout_lvc            = gs_layout
        it_fieldcat_lvc          = gt_fieldcat
        i_save                   = 'A'
      TABLES
        t_outtab                 = gt_alv
      EXCEPTIONS
        program_error            = 1
        OTHERS                   = 2.
  ENDFORM.
  *&---------------------------------------------------------------------*
  *& Form FRM_F4_ZSQID
  *&---------------------------------------------------------------------*
  *& text
  *&---------------------------------------------------------------------*
  *&      <-- P_ZSQID
  *&---------------------------------------------------------------------*
  FORM frm_f4_zsqid  CHANGING p_zsqid.
  
    DATA: BEGIN OF ls_zsqid,
            zsqid     TYPE zmms_zsqid_f4_list-zsqid,          "结算申请
            bukrs     TYPE zmms_zsqid_f4_list-bukrs,          "公司代码
            zzcont    TYPE zmms_zsqid_f4_list-zzcont,         "合同编号
            vrzdt     TYPE zmms_zsqid_f4_list-vrzdt,          "提单日期
            zuser2    TYPE zmms_zsqid_f4_list-zuser2,         "提单人
            settle_no TYPE zmms_zsqid_f4_list-settle_no,      "结算单编号
            zzgxfx    TYPE zmms_zsqid_f4_list-zzgxfx,         "购销方向
            matnr     TYPE zmms_zsqid_f4_list-matnr,          "物料
            bpname    TYPE zmms_zsqid_f4_list-bpname,         "流程主题
            zstatus   TYPE zmms_zsqid_f4_list-zstatus,        "状态描述
          END OF ls_zsqid.
  
    DATA: lt_zsqid LIKE TABLE OF ls_zsqid.
    DATA: lt_field_tab TYPE STANDARD TABLE OF dfies.
    FIELD-SYMBOLS:
          <lfs_zsqid> LIKE LINE OF lt_zsqid.
  
    SELECT zsqid,
           bukrs,
           zzcont,
           vrzdt,
           zuser2,
           settle_no,
           zzgxfx,
           matnr,
           bpname,
           zstatus
     FROM zmmt_settle_head
     INTO TABLE @lt_zsqid.
  
    IF sy-subrc = 0 .
      ls_zsqid-zzgxfx = '采购'.
      MODIFY lt_zsqid FROM ls_zsqid TRANSPORTING zzgxfx WHERE zzgxfx = 'B'.
  
      ls_zsqid-zzgxfx = '销售'.
      MODIFY lt_zsqid FROM ls_zsqid TRANSPORTING zzgxfx WHERE zzgxfx = 'S'.
    ENDIF.
  
    CALL FUNCTION 'F4IF_INT_TABLE_VALUE_REQUEST'
      EXPORTING
        retfield    = 'ZSQID'          "表格要显示的字段
        dynpprog    = sy-repid         "返回才程序
        dynpnr      = sy-dynnr         "屏幕
        dynprofield = 'P_ZSQID'        "往页面回填值的地方
        value_org   = 'S'              "显示类型
      TABLES
        value_tab   = lt_zsqid         "传进去的表格 帮助的内表
        field_tab   = lt_field_tab.
    IF sy-subrc <> 0.
      MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
              WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
    ENDIF.
  
  ENDFORM.
  *&---------------------------------------------------------------------*
  *& Form frm_get_zsqid_number
  *&---------------------------------------------------------------------*
  *& 结算单编号
  *&---------------------------------------------------------------------*
  *& -->  p1        text
  *& <--  p2        text
  *&---------------------------------------------------------------------*
  FORM frm_get_zsqid_number CHANGING pc_a_zsqid TYPE zmmt_settle_head-zsqid
                                     pc_s_return TYPE bapiret2.
  
  
    DATA: lt_head TYPE STANDARD TABLE OF zmmt_settle_head,
          ls_head LIKE LINE OF lt_head.
    DATA: lv_zsqid TYPE zmmt_settle_head-zsqid.
    DATA: lv_zsqid_pat TYPE zmmt_jyys_head-zzcont.
    DATA: lv_zsqid_str TYPE string.
    DATA: lv_buclass TYPE zmmt_jypz_ctype-buclass.
    DATA: lv_num TYPE n LENGTH 4.
    DATA: lv_len TYPE i.
  
  *保存时校验必输字段无误后，自动生成申请单号，
  *如“JS302423080012”，规则：前2位固定为字母“JS”，3~7位为公司代码，
  *7~10位年份后2位+2位月份，最后4位为流水号。
  
    " 开头为JS
    IF p_bukrs = '3024'.
      lv_zsqid = 'JS3024'.
    ENDIF.
  
    " 拼接结算单编号
    lv_zsqid = lv_zsqid && sy-datum+2(4).
    lv_zsqid_pat = lv_zsqid && '%'.
    CONDENSE lv_zsqid_pat.
  
    " 获取流水号
    SELECT zsqid
      FROM zmmt_settle_head
     WHERE zsqid LIKE @lv_zsqid_pat
      INTO CORRESPONDING FIELDS OF TABLE @lt_head.
    IF sy-subrc = 0.
      " 获取最大流水号
      SORT lt_head BY zsqid DESCENDING.
      CLEAR ls_head.
      READ TABLE lt_head INTO ls_head INDEX 1.
      lv_zsqid_str = ls_head-zsqid.
      lv_len = strlen( lv_zsqid_str ) - 4.
      IF lv_len < 0.
        pc_s_return-type = 'E'.
        pc_s_return-message = '生成结算单失败：'&& ls_head-zsqid && '取到的最大结算单编号不正确'.
      ENDIF.
  
      " 根据结算单最后4位编号加1
      TRY.
          lv_num = ls_head-zsqid+lv_len(4).
          lv_num = lv_num + 1.
        CATCH cx_root INTO DATA(lo_root).
          pc_s_return-type = 'E'.
          pc_s_return-message = '生成结算单失败：最后两位不是数字'&& ls_head-zsqid.
      ENDTRY.
  
      CLEAR ls_head.
    ELSE.
      lv_num = 1.
    ENDIF.
  
    " 整合结算单编号
    lv_zsqid = lv_zsqid && lv_num.
    CONDENSE lv_zsqid.
  
    IF lv_zsqid IS INITIAL.
      pc_s_return-type = 'E'.
      pc_s_return-message = '生成结算单失败：请重试'.
    ENDIF.
  
    " 结算单编号重复性检查
    SELECT SINGLE
           zsqid
      INTO ls_head-zsqid
      FROM zmmt_settle_head
     WHERE zsqid = lv_zsqid.
    IF sy-subrc = 0.
      pc_s_return-type = 'E'.
      pc_s_return-message = '生成结算单失败：'&& ls_head-zsqid && '已存在，请重新保存'.
      RETURN.
    ELSE.
      " 锁
      PERFORM frm_lock_zsqid USING lv_zsqid 'L'
                           CHANGING pc_s_return.
  
    ENDIF.
  
    " 结算单赋值
    pc_a_zsqid = lv_zsqid.
  
  ENDFORM.
  *&---------------------------------------------------------------------*
  *& Form frm_lock_zsqid
  *&---------------------------------------------------------------------*
  *& text
  *&---------------------------------------------------------------------*
  *& -->  p1        text
  *& <--  p2        text
  *&---------------------------------------------------------------------*
  FORM frm_lock_zsqid USING pa_zsqid TYPE zmmt_settle_head-zsqid
                             pa_mode
                    CHANGING pc_s_return TYPE bapiret2.
  
    CASE pa_mode.
      WHEN 'L'.
        CALL FUNCTION 'ENQUEUE_EZ_ZB_ZSQID'
          EXPORTING
            mode_zmms_settle_head = 'E'
            mandt                 = sy-mandt
            zsqid                 = pa_zsqid
  *         X_ZSQID               = ' '
  *         _SCOPE                = '2'
  *         _WAIT                 = ' '
  *         _COLLECT              = ' '
          EXCEPTIONS
            foreign_lock          = 1
            system_failure        = 2
            OTHERS                = 3.
  
        IF sy-subrc <> 0.
          pc_s_return-type = 'E'.
          MESSAGE ID sy-msgid TYPE 'S' NUMBER sy-msgno WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4
          INTO pc_s_return-message.
        ENDIF.
  
      WHEN 'U'."Unlock
        CALL FUNCTION 'DEQUEUE_EZ_ZB_ZSQID'
          EXPORTING
            mode_zmms_settle_head = 'E'
            mandt                 = sy-mandt
            zsqid                 = pa_zsqid.
  *   X_ZSQID                     = ' '
  *   _SCOPE                      = '3'
  *   _SYNCHRON                   = ' '
  *   _COLLECT                    = ' '
      WHEN OTHERS.
    ENDCASE.
  
  ENDFORM.
  *&---------------------------------------------------------------------*
  *& Form FRM_GET_DOMAIN_TXT
  *&---------------------------------------------------------------------*
  *& text
  *&---------------------------------------------------------------------*
  *&      --> LT_DD07T_ZSTATUS
  *&      --> P_
  *&---------------------------------------------------------------------*
  FORM frm_get_domain_txt  TABLES pt_dd07t STRUCTURE dd07t
                            USING pa_domname TYPE dd07l-domname.
  
    SELECT domname
           ddtext
           domvalue_l
      FROM dd07t
      INTO CORRESPONDING FIELDS OF TABLE pt_dd07t
     WHERE domname = pa_domname
       AND ddlanguage = sy-langu.
  
  ENDFORM.
  *&---------------------------------------------------------------------*
  *& Form FRM_GETJS_DATA
  *&---------------------------------------------------------------------*
  *& text
  *&---------------------------------------------------------------------*
  *& -->  p1        text
  *& <--  p2        text
  *&---------------------------------------------------------------------*
  FORM frm_getjs_data .
  
    DATA: lt_dd07t_zstatus TYPE STANDARD TABLE OF dd07t,
          ls_dd07t_zstatus LIKE LINE OF lt_dd07t_zstatus.
  
    SELECT a~*,
           b~*
    FROM zmmt_settle_item AS a
    INNER JOIN zmmt_settle_head AS b
    ON a~zsqid = b~zsqid
    WHERE b~bukrs  = @p_bukrs
    AND   b~zzgxfx  IN  @gr_gxfx
    AND   b~settle_typ IN @gr_settyp
    AND   b~zzcont  IN @s_cont
    AND   b~matnr  IN @s_matnr
    AND   b~zsqid IN @s_zsqid
    AND   b~zstatus IN @s_stats
    INTO CORRESPONDING FIELDS OF TABLE @gt_alv.
  
    "  SELECT * FROM zmmt_settle_head
    "  FOR ALL ENTRIES IN @lt_zsqid
    "  WHERE zsqid = @lt_zsqid-zsqid
  *  INTO CORRESPONDING FIELDS OF TABLE @gt_alv.
  
    IF gt_alv IS NOT INITIAL.
  
      PERFORM frm_get_domain_txt TABLES lt_dd07t_zstatus
                                 USING 'ZDZSTATUS'.
      SORT lt_dd07t_zstatus BY domvalue_l.
      LOOP AT gt_alv INTO DATA(ls_alv).
        READ TABLE lt_dd07t_zstatus INTO ls_dd07t_zstatus WITH KEY domvalue_l = ls_alv-zstatus
                                                                              BINARY SEARCH.
        IF sy-subrc = 0.
          ls_alv-zstatus_txt = ls_dd07t_zstatus-ddtext.
        ENDIF.
  
        MODIFY gt_alv FROM ls_alv.
        CLEAR ls_alv.
      ENDLOOP.
    ELSE .
      MESSAGE '查询条件无数据' TYPE 'S' DISPLAY LIKE 'E'.
      LEAVE LIST-PROCESSING .
    ENDIF.
  
  ENDFORM.