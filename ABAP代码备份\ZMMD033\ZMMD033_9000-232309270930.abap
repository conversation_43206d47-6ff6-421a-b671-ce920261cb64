
*&---------------------------------------------------------------------*
*& 包含               ZMMD033_9000
*&---------------------------------------------------------------------*
*&---------------------------------------------------------------------*
*& Module STATUS_9000 OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE status_9000 OUTPUT.
  DATA: lt_comm TYPE TABLE OF sy-ucomm.
  DATA: ls_comm  LIKE LINE OF lt_comm.

  REFRESH lt_comm.
  CLEAR lt_comm.

* IF rb_chg <> 'X'.
*    APPEND 'CHEID' TO lt_comm.
*    CLEAR lt_comm.
*  ENDIF.


  IF rb_chck = 'X'.
    ls_comm =  'UPLOAD'.
    APPEND ls_comm TO lt_comm.
    CLEAR ls_comm.
    ls_comm =  'TJ'.
    APPEND ls_comm TO lt_comm.
    CLEAR ls_comm.
    ls_comm =  'ZCANCEL'.
    APPEND ls_comm TO lt_comm.
    CLEAR ls_comm.
  ENDIF.


  SET PF-STATUS 'S_9000' EXCLUDING lt_comm.
  SET TITLEBAR 'TITLE_9000'.

  " 显示模式
  IF gv_atyp = '2'.
    LOOP AT SCREEN.
      screen-input = 0.
    ENDLOOP.
  ENDIF.

  " 当未勾选【按批次结算】，购销方向为B（采购）
  IF p_pcjs IS INITIAL AND p_gxfx = 'B'.
    "    LOOP AT SCREEN.
    "      IF screen-name CS 'GS_ITEM-WERKS' OR "工厂
    "         screen-name CS 'GS_ITEM-LGORT' OR " 库存地点
    "         screen-name CS 'GS_ITEM-LGOBE' OR " 仓库名称
    "         screen-name CS 'GS_ITEM-VBELN' OR " 交货单
    "         screen-name CS 'GS_ITEM-POSNR' OR " 交货项目
    "         screen-name CS 'GS_ITEM-Z_BATCH_011' OR " 到货批次
    "         screen-name CS 'GS_ITEM-CLABS'.    " 库存数量
    "        " 不可见
    "        screen-active = 0.
    "      ENDIF.
    "      MODIFY SCREEN.
    "    ENDLOOP.
  ENDIF.

  " 当勾选【按批次结算】，购销方向为B（采购）
  IF p_pcjs IS NOT INITIAL AND p_gxfx = 'B'.
    "    LOOP AT SCREEN.
    "      IF screen-name CS 'GS_ITEM-WERKS' OR "工厂
    "         screen-name CS 'GS_ITEM-LGORT' OR " 库存地点
    "         screen-name CS 'GS_ITEM-LGOBE' OR " 仓库名称
    "         screen-name CS 'GS_ITEM-CLABS'.   " 库存数量
    "        " 不可见
    "        screen-active = 0.
    "      ENDIF.
    "      MODIFY SCREEN.
    "    ENDLOOP.
  ENDIF.


  " 当未勾选【按批次结算】，购销方向为S（销售）
  IF p_pcjs IS INITIAL AND p_gxfx = 'S'.
    "    LOOP AT SCREEN.
    "      IF screen-name CS 'GS_ITEM-WERKS' OR "工厂
    "         screen-name CS 'GS_ITEM-LGORT' OR " 库存地点
    "         screen-name CS 'GS_ITEM-LGOBE' OR " 仓库名称
    "         screen-name CS 'GS_ITEM-VBELN' OR " 交货单
    "         screen-name CS 'GS_ITEM-POSNR' OR " 交货项目
    "         screen-name CS 'GS_ITEM-Z_BATCH_011' OR " 到货批次
    "         screen-name CS 'GS_ITEM-CLABS'.    " 库存数量
    "        " 不可见
    "        screen-active = 0.
    "      ENDIF.

    "      MODIFY SCREEN.
    "    ENDLOOP.
  ENDIF.

  " 当勾选【按批次结算】，购销方向为S（销售）
  IF p_pcjs IS NOT INITIAL AND p_gxfx = 'S'.
    "    LOOP AT SCREEN.
    "      IF screen-name CS 'GS_ITEM-VBELN' OR " 交货单
    "         screen-name CS 'GS_ITEM-POSNR' OR " 交货项目
    "         screen-name CS 'GS_ITEM-SQTY1'.   " 已结算数量
    "        " 不可见
    "        screen-active = 0.
    "      ENDIF.
    "      MODIFY SCREEN.
    "    ENDLOOP.
  ENDIF.

  " 当勾选【按批次结算】，购销方向为S（销售），结算类型 = '2'（二次结算）
  IF p_pcjs IS NOT INITIAL AND p_gxfx = 'S' AND p_stltyp = '2'.
    "    LOOP AT SCREEN.
    "      IF screen-name CS 'GS_ITEM-WERKS' OR "工厂
    "         screen-name CS 'GS_ITEM-LGORT' OR " 库存地点
    "         screen-name CS 'GS_ITEM-LGOBE' OR " 仓库名称
    "         screen-name CS 'GS_ITEM-CLABS' OR " 库存数量
    "         screen-name CS 'GS_ITEM-MENGE'.   " 订单数量
    "        " 不可见
    "        screen-active = 0.
    "      ENDIF.
    "      MODIFY SCREEN.
    "    ENDLOOP.
  ENDIF.

  IF gv_init IS INITIAL.
    " 设置长文本控件
    gv_init = 'X'.
    CREATE OBJECT: go_container EXPORTING container_name = 'GO_TXT'. " 文本控件
    CREATE OBJECT go_editor
      EXPORTING
        parent            = go_container
        wordwrap_mode     = cl_gui_textedit=>wordwrap_at_fixed_position
***Started on 20230419 with REQ2023031700534 by DTT_FENG - MOD,
*       WORDWRAP_POSITION = 256
        wordwrap_position = 75
***Ended on 20230419 with REQ2023031700534 by DTT_FENG - MOD.
*       WORDWRAP_TO_LINEBREAK_MODE = CL_GUI_TEXTEDIT=>TRUE
      .

    CALL METHOD go_editor->set_toolbar_mode  "去掉工具栏
      EXPORTING
        toolbar_mode = 0.
    CALL METHOD go_editor->set_statusbar_mode "去掉状态栏
      EXPORTING
        statusbar_mode = 0.
  ENDIF.
  CALL METHOD go_editor->set_text_as_r3table "写数据
    EXPORTING
      table = gt_m1[].

  " 显示模式
  IF gv_atyp = '2'.
    LOOP AT  SCREEN .
      screen-input = 0.
      MODIFY SCREEN.
    ENDLOOP.

    "长文本框不可编辑
    CALL METHOD go_editor->set_readonly_mode
      EXPORTING
        readonly_mode          = '1'
      EXCEPTIONS
        error_cntl_call_method = 1
        invalid_parameter      = 2
        OTHERS                 = 3.
    IF sy-subrc <> 0.
*     Implement suitable error handling here
    ENDIF.
  ELSE.
    "长文本框可编辑
    CALL METHOD go_editor->set_readonly_mode
      EXPORTING
        readonly_mode          = '0'
      EXCEPTIONS
        error_cntl_call_method = 1
        invalid_parameter      = 2
        OTHERS                 = 3.
    IF sy-subrc <> 0.
*     Implement suitable error handling here
    ENDIF.
  ENDIF.



ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  USER_COMMAND_9000  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE user_command_9000 INPUT.
  DATA lc_menge TYPE  ekpo-menge.
  DATA: lv_ucomm TYPE sy-ucomm.
  lv_ucomm = gv_okcode.
  CLEAR gv_okcode.
  CASE lv_ucomm.
    WHEN 'SAVE'.    "保存数据
      PERFORM frm_save_data.
    WHEN 'BACK'.
      CLEAR: gs_head,gt_item.
      LEAVE TO SCREEN 0 .
    WHEN 'UPLOAD'.   " 附件上传
      IF gs_head-zsqid IS NOT INITIAL .
        SUBMIT zfid053 WITH p_zdjbh = gs_head-zsqid WITH p_zydlx = 'ZMMD033' AND RETURN .
      ELSE.
        MESSAGE '结算申请为空，请生成后再上传' TYPE 'E'.
      ENDIF.
    WHEN 'TJ'.        " 提交
      PERFORM frm_tjjs.
    WHEN 'ZCANCEL'.   "SAP作废
      " 作废合结算申请
      PERFORM frm_cancel_js.
    WHEN 'CHANJS'.    " 指派订单行
      PERFORM frm_chanjs.
    WHEN 'ZCOPY'.
      PERFORM zcopy_item.
    WHEN OTHERS.
      CLEAR lc_menge.
      LOOP AT gt_item INTO gs_item.
        lc_menge = lc_menge + gs_item-settle_qty.
      ENDLOOP.
      gs_head-settle_qty = lc_menge.
  ENDCASE.
ENDMODULE.
FORM zcopy_item.
  DATA lc_line TYPE i.
  DATA: lt_item TYPE TABLE OF ty_item,
        lw_item TYPE ty_item.
  CLEAR lc_line.
  LOOP AT gt_item INTO gs_item WHERE box IS NOT INITIAL.
    lc_line = lc_line + 1.
  ENDLOOP.
  IF lc_line > 1.
    MESSAGE '最多选择一行复制！' TYPE 'E'.
  ELSE.
    CLEAR: lt_item, lw_item.
    APPEND LINES OF gt_item TO lt_item.
    SORT lt_item BY order_no order_item DESCENDING.
    READ TABLE lt_item INTO lw_item INDEX 1.
    gs_item-order_item = lw_item-order_item + 10.
    APPEND gs_item TO gt_item.
  ENDIF.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form frm_save_data
*&---------------------------------------------------------------------*
*& 保存数据
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_save_data.
  DATA: lv_num(2) TYPE n.
  DATA: lv_len TYPE i .
  DATA: lv_zsqid TYPE zmmt_settle_head-zsqid.
  DATA: ls_zmmt_settle_head TYPE zmmt_settle_head.
  DATA: ls_zmmt_settle_item TYPE zmmt_settle_item.
  DATA: lt_zmmt_settle_item TYPE TABLE OF zmmt_settle_item.
  DATA: lt_item TYPE TABLE OF ty_item.
  DATA: lv_item_no TYPE zmmt_settle_item-item_no.
  DATA: ls_return TYPE bapiret2.
  DATA: ls_header TYPE thead.
  DATA: lt_ltxts TYPE TABLE OF tline.      " 检查行
  DATA: ls_ltxts TYPE tline,
        lc_tab   TYPE i.

  " 检查数据
  " 申请人必填
  IF gs_head-zuser1 IS INITIAL.
    MESSAGE '申请人必填' TYPE 'E'.
  ENDIF.
  " 印章种类必填
  IF gs_head-sealcatx IS INITIAL.
    MESSAGE '印章种类必填' TYPE 'E'.
  ENDIF.
  " 流程主题必填
  IF gs_head-bpname IS INITIAL.
    MESSAGE '流程主题必填' TYPE 'E'.
  ENDIF.
  "本次结算数量必填
  IF gs_item-settle_qty IS INITIAL.
    MESSAGE '本次结算数量必填' TYPE 'E'.
  ENDIF.
  " 订单单价必填
  IF gs_item-ord_price IS INITIAL.
    MESSAGE '订单单价必填' TYPE 'E'.
  ENDIF.
  " 结算单价必填
  IF gs_item-oth_price IS INITIAL.
    MESSAGE '结算单价必填' TYPE 'E'.
  ENDIF.
  " 货品类型必填
  IF gs_item-zhplx IS INITIAL.
    MESSAGE '货品类型必填' TYPE 'E'.
  ENDIF.
  " 更新方式必填
  IF gs_item-update_typ IS INITIAL.
    MESSAGE '更新方式必填' TYPE 'E'.
  ENDIF.

  " 提单人
  SELECT SINGLE aduser
    FROM zfit_user_domain
   WHERE uname EQ @sy-uname
    INTO @gs_head-zuser2.

  " 申请人
  SELECT SINGLE aduser
    FROM zfit_user_domain
   WHERE uname EQ @sy-uname
    INTO @gs_head-zuser1.

  " 结算单编号自动创建
  IF gs_head-zsqid IS INITIAL.
    CLEAR:ls_return,lv_zsqid.
    PERFORM frm_get_zsqid_number CHANGING lv_zsqid
                                          ls_return.
    IF ls_return-type = 'E'.
      MESSAGE ls_return-message TYPE 'E'.
    ENDIF.

    "抬头字段赋值
    gs_head-zsqid = lv_zsqid .
    gs_head-zcnam = sy-uname .
    gs_head-zcdat = sy-datum .
    gs_head-zctim = sy-uzeit .
    gs_head-settle_typ = p_stltyp.


    " 行项目赋值
    lv_item_no = 1.
    CLEAR lc_tab.
    LOOP AT gt_item INTO DATA(ls_item).
      lc_tab = lc_tab + 1.
      ls_item-zcnam = sy-uname .
      ls_item-zcdat = sy-datum .
      ls_item-zctim = sy-uzeit .
      ls_item-zunam = sy-uname .
      ls_item-zudat = sy-datum .
      ls_item-zutim = sy-uzeit .

      IF ls_return-type = 'E'..
        MESSAGE ls_return-message TYPE 'S' DISPLAY LIKE 'E'.
        RETURN.
      ENDIF.

      MOVE-CORRESPONDING ls_item TO ls_zmmt_settle_item.
      ls_zmmt_settle_item-item_no = lc_tab.
      ls_zmmt_settle_item-zsqid = gs_head-zsqid.
      APPEND ls_zmmt_settle_item TO lt_zmmt_settle_item.
      CLEAR ls_item.
      CLEAR ls_zmmt_settle_item.
      ADD 1 TO lv_item_no.
    ENDLOOP.
    " 修改
  ELSE.
    gs_head-zunam = sy-uname .
    gs_head-zudat = sy-datum .
    gs_head-zutim = sy-uzeit .
    " 计算最大行项目号
    APPEND LINES OF gt_itemdel TO gt_item.
    SORT gt_item BY item_no DESCENDING.
    READ TABLE gt_item INTO ls_item INDEX 1 .
*    lv_zitiem = ls_item-zitiem.
*    SORT gt_item BY zitiem ASCENDING.
    CLEAR lc_tab.
    LOOP AT gt_item INTO ls_item.
      lc_tab = lc_tab + 1.
      MOVE-CORRESPONDING gs_head TO ls_zmmt_settle_head.
      " 创建信息
      IF ls_item-item_no EQ 0.
        ls_item-item_no = lv_item_no + 1.
        lv_item_no = lv_item_no + 1.
        ls_item-zcnam = sy-uname .
        ls_item-zcdat = sy-datum .
        ls_item-zctim = sy-uzeit .
        ls_item-zunam = sy-uname .
        ls_item-zudat = sy-datum .
        ls_item-zutim = sy-uzeit .
      ELSE.
        ls_item-zunam = sy-uname .
        ls_item-zudat = sy-datum .
        ls_item-zutim = sy-uzeit .
      ENDIF .

      MOVE-CORRESPONDING ls_item TO ls_zmmt_settle_item.
      ls_zmmt_settle_item-item_no = lc_tab.
      ls_zmmt_settle_item-zsqid = gs_head-zsqid.
      APPEND ls_zmmt_settle_item TO lt_zmmt_settle_item.
      MODIFY gt_item FROM ls_item.
      CLEAR ls_item.
      CLEAR ls_zmmt_settle_item.
    ENDLOOP.
    SORT gt_item BY item_no ASCENDING." 正向排序
  ENDIF.

  " 更新自建表数据
  IF ls_zmmt_settle_head-zstatus IS INITIAL .
    gs_head-zstatus = '00'.
    ls_zmmt_settle_head-zstatus = '00'.
    DATA(lv_statustxt) = '保存'.
  ENDIF.

  MOVE-CORRESPONDING gs_head TO ls_zmmt_settle_head.
  ls_zmmt_settle_head-partner = gs_head-partner.
  "更新数据库表
  MODIFY zmmt_settle_head FROM ls_zmmt_settle_head.
  MODIFY zmmt_settle_item FROM TABLE lt_zmmt_settle_item.
  IF sy-subrc = 0 .
    COMMIT WORK AND WAIT .

    " 结算备注摘要 保存长本文
    IF gt_m1 IS NOT INITIAL .
      ls_header-tdobject = 'ZMM_JSSQ'.
      ls_header-tdid     = 'LTXT'.
      ls_header-tdspras  = sy-langu.
      ls_header-tdname   = ls_zmmt_settle_head-zsqid.
      LOOP AT gt_m1 INTO DATA(ls_m1).
        ls_ltxts-tdformat = '*'.
        ls_ltxts-tdline = ls_m1.
        APPEND ls_ltxts TO lt_ltxts.
        CLEAR: ls_ltxts,ls_m1.
      ENDLOOP.

      CALL FUNCTION 'SAVE_TEXT'
        EXPORTING
          client          = sy-mandt
          header          = ls_header
          savemode_direct = 'X'
        TABLES
          lines           = lt_ltxts
        EXCEPTIONS
          id              = 1
          language        = 2
          name            = 3
          object          = 4
          OTHERS          = 5.
    ENDIF.




    gv_atyp = '2'.
    MESSAGE '数据保存成功' TYPE 'S'.
  ELSE.
    ROLLBACK WORK.
    MESSAGE '数据保存失败' TYPE 'E'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Module T_TABLE_CHANGE_TC_ATTR OUTPUT
*&---------------------------------------------------------------------*
*&"----------------TABLE CONTROL 相关----------------------------“
*&---------------------------------------------------------------------*
CONTROLS: t_table TYPE TABLEVIEW USING SCREEN 9000.

DATA: g_t_table_lines  LIKE sy-loopc.
DATA: ok_code LIKE sy-ucomm.

MODULE t_table_change_tc_attr OUTPUT.
  DESCRIBE TABLE gt_item LINES t_table-lines.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  T_TABLE_USER_COMMAND  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE t_table_user_command INPUT.
  DATA: lv_code9000 TYPE sy-ucomm.
  lv_code9000 = gv_code9000.
  ok_code = sy-ucomm.


  PERFORM user_ok_tc USING 'T_TABLE' 'GT_ITEM' 'BOX'
                     CHANGING ok_code.
  sy-ucomm = ok_code.
  "获取文本框数据
  CALL METHOD go_editor->get_text_as_r3table
    IMPORTING
      table = gt_m1.

  CLEAR gv_code9000.
  CASE lv_code9000.
    WHEN 'TJ'.          " 提交
      PERFORM frm_tjjs.
    WHEN 'BACK'.        " 后退
      CLEAR:gs_head,gt_item,gt_m1.
      LEAVE TO SCREEN 0.
  ENDCASE.
  sy-ucomm = ok_code.

ENDMODULE.
*&---------------------------------------------------------------------*
*& Form user_ok_tc
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM user_ok_tc USING   p_tc_name TYPE dynfnam
                         p_table_name
                         p_mark_name
                CHANGING p_ok LIKE sy-ucomm.

  DATA: l_ok     TYPE sy-ucomm,
        l_offset TYPE i.

*&SPWIZARD: Table control specific operations
*&SPWIZARD: evaluate TC name and operations
  SEARCH p_ok FOR p_tc_name.
  IF sy-subrc <> 0.
    EXIT.
  ENDIF.
  l_offset = strlen( p_tc_name ) + 1.
  l_ok = p_ok+l_offset.
*&SPWIZARD: execute general and TC specific operations
  CASE l_ok.
    WHEN 'INSR'.                      "insert row
      PERFORM fcode_insert_row USING p_tc_name p_table_name.
      CLEAR p_ok.
    WHEN 'DELE'.                      "delete row
      PERFORM fcode_delete_row USING    p_tc_name
                                        p_table_name
                                        p_mark_name.
      CLEAR p_ok.
    WHEN 'P--' OR                     "top of list
         'P-'  OR                     "previous page
         'P+'  OR                     "next page
         'P++'.                       "bottom of list
      PERFORM compute_scrolling_in_tc USING p_tc_name
                                            l_ok.
      CLEAR p_ok.
    WHEN 'MARK'.                      "mark all filled lines
      PERFORM fcode_tc_mark_lines USING p_tc_name
                                        p_table_name
                                        p_mark_name   .
      CLEAR p_ok.
    WHEN 'DMRK'.                      "demark all filled lines
      PERFORM fcode_tc_demark_lines USING p_tc_name
                                          p_table_name
                                          p_mark_name .
      CLEAR p_ok.
  ENDCASE.
ENDFORM.
*&---------------------------------------------------------------------*
*& Form fcode_insert_row
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM fcode_insert_row
              USING    p_tc_name       TYPE dynfnam
                       p_table_name.
  DATA l_lines_name       LIKE feld-name.
  DATA l_selline          LIKE sy-stepl.
  DATA l_lastline         TYPE i.
  DATA l_line             TYPE i.
  DATA l_table_name       LIKE feld-name.
  FIELD-SYMBOLS <tc>      TYPE cxtab_control.
  FIELD-SYMBOLS <table>   TYPE STANDARD TABLE.
  FIELD-SYMBOLS <lines>   TYPE i.

  ASSIGN (p_tc_name) TO <tc>.
*&SPWIZARD: get the table, which belongs to the tc                     *
  CONCATENATE p_table_name '[]' INTO l_table_name. "table body
  ASSIGN (l_table_name) TO <table>.                "not headerline

*&SPWIZARD: get looplines of TableControl                              *
  CONCATENATE 'G_' p_tc_name '_LINES' INTO l_lines_name.
  ASSIGN (l_lines_name) TO <lines>.

*&SPWIZARD: get current line                                           *
  GET CURSOR LINE l_selline.
  IF sy-subrc <> 0.                   " append line to table
    l_selline = <tc>-lines + 1.
*&SPWIZARD: set top line                                               *
    IF l_selline > <lines>.
      <tc>-top_line = l_selline - <lines> + 1 .
    ELSE.
      <tc>-top_line = 1.
    ENDIF.
  ELSE.                               " insert line into table
    l_selline = <tc>-top_line + l_selline - 1.
    l_lastline = <tc>-top_line + <lines> - 1.
  ENDIF.
*&SPWIZARD: set new cursor line                                        *
  l_line = l_selline - <tc>-top_line + 1.

*&SPWIZARD: insert initial line                                        *
  INSERT INITIAL LINE INTO <table> INDEX l_selline.
  <tc>-lines = <tc>-lines + 1.
*&SPWIZARD: set cursor                                                 *
  SET CURSOR LINE l_line.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form fcode_delete_row
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM fcode_delete_row
              USING    p_tc_name    TYPE dynfnam
                       p_table_name
                       p_mark_name.

*&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
  DATA l_table_name       LIKE feld-name.
  DATA ls_itemdel TYPE ty_item.
  FIELD-SYMBOLS <tc>      TYPE cxtab_control.
  FIELD-SYMBOLS <table>   TYPE STANDARD TABLE.
  FIELD-SYMBOLS <wa>.
  FIELD-SYMBOLS <mark_field>.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

  ASSIGN (p_tc_name) TO <tc>.

*&SPWIZARD: get the table, which belongs to the tc                     *
  CONCATENATE p_table_name '[]' INTO l_table_name. "table body
  ASSIGN (l_table_name) TO <table>.                "not headerline

*&SPWIZARD: delete marked lines                                        *
  DESCRIBE TABLE <table> LINES <tc>-lines.

  LOOP AT <table> ASSIGNING <wa>.

*&SPWIZARD: access to the component 'FLAG' of the table header         *
    ASSIGN COMPONENT p_mark_name OF STRUCTURE <wa> TO <mark_field>.

    IF <mark_field> = 'X'.
      " 删除标识
      MOVE-CORRESPONDING <wa> TO ls_itemdel.
*      ls_itemdel-zdelfl = 'X'.
      APPEND ls_itemdel TO gt_itemdel .
      CLEAR  ls_itemdel.

      DELETE <table> INDEX syst-tabix.
      IF sy-subrc = 0.
        <tc>-lines = <tc>-lines - 1.
      ENDIF.
    ENDIF.
  ENDLOOP.

ENDFORM.                              " FCODE_DELETE_ROW
*&---------------------------------------------------------------------*
*& Form compute_scrolling_in_tc
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM compute_scrolling_in_tc USING   p_tc_name
                                      p_ok.
*&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
  DATA l_tc_new_top_line     TYPE i.
  DATA l_tc_name             LIKE feld-name.
  DATA l_tc_lines_name       LIKE feld-name.
  DATA l_tc_field_name       LIKE feld-name.

  FIELD-SYMBOLS <tc>         TYPE cxtab_control.
  FIELD-SYMBOLS <lines>      TYPE i.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

  ASSIGN (p_tc_name) TO <tc>.
*&SPWIZARD: get looplines of TableControl                              *
  CONCATENATE 'G_' p_tc_name '_LINES' INTO l_tc_lines_name.
  ASSIGN (l_tc_lines_name) TO <lines>.


*&SPWIZARD: is no line filled?                                         *
  IF <tc>-lines = 0.
*&SPWIZARD: yes, ...                                                   *
    l_tc_new_top_line = 1.
  ELSE.
*&SPWIZARD: no, ...                                                    *
    CALL FUNCTION 'SCROLLING_IN_TABLE'
      EXPORTING
        entry_act      = <tc>-top_line
        entry_from     = 1
        entry_to       = <tc>-lines
        last_page_full = 'X'
        loops          = <lines>
        ok_code        = p_ok
        overlapping    = 'X'
      IMPORTING
        entry_new      = l_tc_new_top_line
      EXCEPTIONS
*       NO_ENTRY_OR_PAGE_ACT  = 01
*       NO_ENTRY_TO    = 02
*       NO_OK_CODE_OR_PAGE_GO = 03
        OTHERS         = 0.
  ENDIF.

*&SPWIZARD: get actual tc and column                                   *
  GET CURSOR FIELD l_tc_field_name
             AREA  l_tc_name.

  IF syst-subrc = 0.
    IF l_tc_name = p_tc_name.
*&SPWIZARD: et actual column                                           *
      SET CURSOR FIELD l_tc_field_name LINE 1.
    ENDIF.
  ENDIF.

*&SPWIZARD: set the new top line                                       *
  <tc>-top_line = l_tc_new_top_line.
ENDFORM.                              " COMPUTE_SCROLLING_IN_TC
*&---------------------------------------------------------------------*
*& Form fcode_tc_mark_lines
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM fcode_tc_mark_lines USING p_tc_name
                               p_table_name
                               p_mark_name.
*&SPWIZARD: EGIN OF LOCAL DATA-----------------------------------------*
  DATA l_table_name       LIKE feld-name.

  FIELD-SYMBOLS <tc>         TYPE cxtab_control.
  FIELD-SYMBOLS <table>      TYPE STANDARD TABLE.
  FIELD-SYMBOLS <wa>.
  FIELD-SYMBOLS <mark_field>.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

  ASSIGN (p_tc_name) TO <tc>.

*&SPWIZARD: get the table, which belongs to the tc                     *
  CONCATENATE p_table_name '[]' INTO l_table_name. "table body
  ASSIGN (l_table_name) TO <table>.                "not headerline

*&SPWIZARD: mark all filled lines                                      *
  LOOP AT <table> ASSIGNING <wa>.

*&SPWIZARD: access to the component 'FLAG' of the table header         *
    ASSIGN COMPONENT p_mark_name OF STRUCTURE <wa> TO <mark_field>.

    <mark_field> = 'X'.
  ENDLOOP.
ENDFORM.                                          "fcode_tc_mark_lines
*&---------------------------------------------------------------------*
*& Form fcode_tc_demark_lines
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM fcode_tc_demark_lines USING p_tc_name
                                 p_table_name
                                 p_mark_name .
*&SPWIZARD: BEGIN OF LOCAL DATA----------------------------------------*
  DATA l_table_name       LIKE feld-name.

  FIELD-SYMBOLS <tc>         TYPE cxtab_control.
  FIELD-SYMBOLS <table>      TYPE STANDARD TABLE.
  FIELD-SYMBOLS <wa>.
  FIELD-SYMBOLS <mark_field>.
*&SPWIZARD: END OF LOCAL DATA------------------------------------------*

  ASSIGN (p_tc_name) TO <tc>.

*&SPWIZARD: get the table, which belongs to the tc                     *
  CONCATENATE p_table_name '[]' INTO l_table_name. "table body
  ASSIGN (l_table_name) TO <table>.                "not headerline

*&SPWIZARD: demark all filled lines                                    *
  LOOP AT <table> ASSIGNING <wa>.

*&SPWIZARD: access to the component 'FLAG' of the table header         *
    ASSIGN COMPONENT p_mark_name OF STRUCTURE <wa> TO <mark_field>.

    <mark_field> = space.
  ENDLOOP.
ENDFORM.                                          "fcode_tc_mark_lines
*&---------------------------------------------------------------------*
*&      Module  USER_COMMAND_EXIT_9000  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE user_command_exit_9000 INPUT.
  IF sy-ucomm = 'EXIT'.
    LEAVE TO SCREEN 0.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Form frm_tjjs
*&---------------------------------------------------------------------*
*& 提交结算单审批
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_tjjs.
  DATA: ls_zmmt_settle_head TYPE zmmt_settle_head.
  DATA: lt_zmmt_settle_item TYPE TABLE OF zmmt_settle_item.

  IF gs_head-zsqid IS INITIAL.
    MESSAGE '请先保存数据再提交审批' TYPE 'E'.
  ENDIF.

  " 调用接口发送数据
  PERFORM frm_send_to_k2.

  " 保存数据
  MOVE-CORRESPONDING gt_item TO lt_zmmt_settle_item.
  MOVE-CORRESPONDING gs_head TO ls_zmmt_settle_head.
  "更新数据库表
  MODIFY zmmt_settle_head FROM  ls_zmmt_settle_head.
  MODIFY zmmt_settle_item FROM TABLE lt_zmmt_settle_item.
  IF sy-subrc = 0 .
    MESSAGE '数据提交成功' TYPE 'S'.
    COMMIT WORK .
  ELSE.
    ROLLBACK WORK .
  ENDIF.
  CLEAR:lt_zmmt_settle_item,ls_zmmt_settle_head.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form frm_send_to_k2
*&---------------------------------------------------------------------*
*& 调用接口发送数据至K2系统审批
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_send_to_k2 .
  DATA:ls_log TYPE zfit_k2_log.
  DATA:lv_nr TYPE inri-nrrangenr VALUE '01'.
  DATA:lv_json TYPE string .
  DATA:lv_type TYPE bapi_mtype,
       lv_msg  TYPE bapi_msg,
       lv_k2id TYPE  zde_ry1_k2id.
  DATA:lv_return_json TYPE string. "K2返回JSON
  DATA:lv_url TYPE string.         "K2返回URL

  " K2 日志id
  CALL FUNCTION 'NUMBER_RANGE_ENQUEUE'
    EXPORTING
      object           = 'ZRY1_K2LOG'
    EXCEPTIONS
      foreign_lock     = 1
      object_not_found = 2
      system_failure   = 3
      OTHERS           = 4.
*  如果号码范围存在
  IF sy-subrc EQ 0 .
    CALL FUNCTION 'NUMBER_GET_NEXT'
      EXPORTING
        nr_range_nr             = lv_nr
        object                  = 'ZRY1_K2LOG'
*       QUANTITY                = '1'
*       SUBOBJECT               = ' '
*       TOYEAR                  = '0000'
        ignore_buffer           = 'X'
      IMPORTING
        number                  = ls_log-zlogid
*       QUANTITY                =
*       RETURNCODE              =
      EXCEPTIONS
        interval_not_found      = 1
        number_range_not_intern = 2
        object_not_found        = 3
        quantity_is_0           = 4
        quantity_is_not_1       = 5
        interval_overflow       = 6
        buffer_overflow         = 7
        OTHERS                  = 8.
    IF sy-subrc <> 0.
*      LV_TYPE = 'E'.
      MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
      WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4 .
    ELSE.
      CALL FUNCTION 'NUMBER_RANGE_DEQUEUE'
        EXPORTING
          object           = 'ZRY1_K2LOG'
        EXCEPTIONS
          object_not_found = 1
          OTHERS           = 2.
    ENDIF.
  ELSE.
*    LV_TYPE = 'E'.
    MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
    WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4 .
*    RETURN.
  ENDIF.

  PERFORM frm_json_data CHANGING lv_json lv_type lv_msg." 传入json处理

  "记日志
  IF lv_type <> 'E'.
    ls_log-jkfx = '1'.
    ls_log-zsqid = gs_head-zsqid.
    zry1_cl_k2_common=>k2_save_log( EXPORTING iv_rzjd = '01' iv_json = lv_json is_info = ls_log  ).
    " 当K2ID不为空时，驳回申请单重新发起流程
    IF gs_head-k2id IS NOT INITIAL.
      zry1_cl_k2_common=>k2_re_start_flow( EXPORTING iv_json = lv_json IMPORTING ev_type = lv_type ev_msg = lv_msg ev_k2id = lv_k2id ev_json = lv_return_json ev_url = lv_url ).
    ELSE.
      "发起流程
      zry1_cl_k2_common=>k2_start_flow( EXPORTING iv_json = lv_json IMPORTING ev_type = lv_type ev_msg = lv_msg ev_k2id = lv_k2id ev_json = lv_return_json ev_url = lv_url ).
    ENDIF.
    ls_log-k2id  = lv_k2id.
    ls_log-rtype = lv_type.
    ls_log-rtmsg = lv_msg.
    ls_log-jkfx = '2'.
    gs_head-k2id = lv_k2id.
    gs_head-zstatus = '10'.
    zry1_cl_k2_common=>k2_save_log( EXPORTING iv_rzjd = '02' iv_json = lv_return_json is_info = ls_log ).
  ENDIF.

  IF lv_type = 'E' .
    MESSAGE '提交审批失败:' && lv_msg TYPE 'E'.
  ELSEIF lv_type = 'S'.
    gs_head-zstatus = '20'.
    MESSAGE '提交审批成功' TYPE 'S'.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form frm_json_data
*&---------------------------------------------------------------------*
*& SAP接口传值
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_json_data  CHANGING pv_json TYPE string
                              pv_type TYPE bapi_mtype
                              pv_msg  TYPE bapi_msg  .

  DATA: lt_suminfo TYPE zmms_qh1_k2_suminfo_req .
  DATA: ls_suminfo LIKE LINE OF lt_suminfo.
  DATA: lv_suminfo TYPE string.
  "结算明细
  DATA: lt_jsitem TYPE zmms_qh1_k2_zsqid_item.
  DATA: ls_jsitem LIKE LINE OF lt_jsitem.
  DATA: lv_jsitem TYPE string.
  "附件路径
  DATA: lt_filelist TYPE zmms_qh1_k2_filelist_req.
  DATA: ls_filelist LIKE LINE OF lt_filelist.
  DATA: lv_filelist TYPE string.
  "交易员
  DATA: ls_datafield TYPE zmms_qh1_k2_datafield.
  DATA: lv_datafield TYPE string .
  DATA: lv_formdata  TYPE string.
  DATA: lv_bizdata   TYPE string.
  DATA:lt_name_mappings TYPE /ui2/cl_json=>name_mappings.
  DATA: BEGIN OF ls_jdata,
          processcode   TYPE string,
          sourceid      TYPE string,
          startmode     TYPE string,
          applyusercode TYPE string,
          applypostcode TYPE string,
          applydeptcode TYPE string,
          startusercode TYPE string,
          startdeptcode TYPE string,
          folio         TYPE string,
          bizid         TYPE string,
          bizdata       TYPE string,
          attr1         TYPE string,
          attr2         TYPE string,
          attr3         TYPE string,
        END OF ls_jdata.
  DATA: BEGIN OF ls_bizdata ,
          formurl       TYPE string,
          formmobileurl TYPE string,
          formdata      TYPE string,
          datafield     TYPE string,
        END OF ls_bizdata .
  DATA: BEGIN OF ls_formdata,
          zsqid           TYPE string,                  "申请单号
          bpname          TYPE string,                  "主题
          butxt           TYPE string,                  "公司名称
          settle_no       TYPE string,                  "结算单号
          buname          TYPE string,                  "客商名称
          zconame         TYPE string,                  "合同名称
          zzcont1         TYPE string,                  "客户合同编号
          zzcont          TYPE string,                  "合同编号
          zzgxfx          TYPE string,                  "合同类别
          sealcatx        TYPE string,                  "印章种类
          maktx           TYPE string,                  "品名
          zitem           TYPE string,                   "项目ID
          zzitemt         TYPE string,                  "项目名称
          zvkgrp_txt      TYPE string,                  "交易员（姓名）
          settle_amt      TYPE string,                  "结算金额
          settle_qty      TYPE string,                  "结算数量
          settle_unit     TYPE string,                  "结算单位
          suminfo         TYPE string,                  "内容摘要
          zmmt_zsqid_item TYPE zmms_qh1_k2_zsqid_item,  "合同明细
          filelist        TYPE zmms_qh1_k2_filelist_req, "附件路径
          xgshtt          TYPE string,                  "是否为格式合同描述
          xcdfwt          TYPE string,                  "是否为仓单服务业务下游客户描述
        END OF ls_formdata.

  DATA: BEGIN OF ls_jyhead,
          zconame    TYPE zmmt_jyys_head-zconame,
          zzcont     TYPE zmmt_jyys_head-zzcont,
          zzcont1    TYPE zmmt_jyys_head-zzcont1,
          zitem      TYPE zmmt_jyys_head-zitem,
          zzitemt    TYPE zmmt_jyys_head-zzitemt,
          zzvkgrp    TYPE zmmt_jyys_head-zzvkgrp,
          zvkgrp_txt TYPE zmmt_jyys_head-zvkgrp_txt,
          xgsht      TYPE zmmt_htys_head-xgsht,
          xcdfw      TYPE zmmt_htys_head-xcdfw,
        END OF ls_jyhead.

*  DATA: ls_head TYPE ty_jshead.

  " 转json
  lt_name_mappings = VALUE #(
   ( abap = 'PROCESSCODE' json = 'processCode' ) ( abap = 'SOURCEID' json = 'sourceId' )
   ( abap = 'STARTMODE' json = 'startMode' ) ( abap = 'APPLYUSERCODE' json = 'applyUserCode' )
   ( abap = 'APPLYPOSTCODE' json = 'applyPostCode' ) ( abap = 'APPLYDEPTCODE' json = 'applyDeptCode' )
   ( abap = 'STARTUSERCODE' json = 'startUserCode' ) ( abap = 'STARTDEPTCODE' json = 'startDeptCode' )
   ( abap = 'FOLIO' json = 'folio' ) ( abap = 'BIZID' json = 'bizId' )
   ( abap = 'BIZDATA' json = 'bizData' ) ( abap = 'FORMURL' json = 'formUrl' )
   ( abap = 'FORMMOBILEURL' json = 'formMobileUrl' ) ( abap = 'FORMDATA' json = 'formData' )
   ( abap = 'DATAFIELD' json = 'dataField' ) (  abap = 'FILENAME' json = 'fileName'  )
   ( abap = 'ATTR1' json = 'Attr1' ) ( abap = 'ATTR2' json = 'Attr2' )
   ( abap = 'ATTR3' json = 'Attr3' ) ( abap = 'FILEID' json = 'fileId' )
                          ).
  "流程编码
  ls_jdata-processcode = 'YX-ERP-JSSQLC'. "结算单审批流程
  "系统来源
  SELECT SINGLE sys_code FROM zfit_bus_unit
    WHERE bukrs = @gs_head-bukrs
    INTO @ls_jdata-sourceid.
  " 申请人ID(主数据员工工号)
  ls_jdata-applyusercode = gs_head-zuser1.
  "发起人ID
  ls_jdata-startusercode = gs_head-zuser2.
  "流程主题
  ls_jdata-folio = gs_head-bpname.
  "业务数据唯一标识
  ls_jdata-bizid = gs_head-zsqid.

  SELECT SINGLE
         a~zconame,
         b~zzcont,
         a~zzcont1,
         a~zitem,
         a~zzitemt,
         a~zzvkgrp,
         a~zvkgrp_txt,
         d~xgsht,
         d~xcdfw
    FROM zmmt_jyys_head AS a
    INNER JOIN zmmt_settle_head AS b ON a~zzcont = b~zzcont
    INNER JOIN zmmt_htys_item AS c ON a~zzcont = c~zzcont
    INNER JOIN zmmt_htys_head AS d ON c~zsqid = d~zsqid
    WHERE b~zstatus <> '05'
      INTO @ls_jyhead.


  " 申请单抬头数据整理
  ls_formdata-zsqid = gs_head-zsqid.          " 申请单号
  ls_formdata-bpname = gs_head-bpname.        " 主题
  ls_formdata-butxt = gs_head-butxt.          " 公司名称
  ls_formdata-buname = gs_head-buname.        " 客商名称
  ls_formdata-settle_no = gs_head-settle_no.  " 结算单编号
  ls_formdata-zconame = ls_jyhead-zconame.      " 合同名称
  ls_formdata-zzcont = ls_jyhead-zzcont.        " 合同编号
  ls_formdata-zzcont1 = ls_jyhead-zzcont1.      " 客户合同编号
  ls_formdata-zzgxfx = gs_head-zzgxfx.        " 合同类别
  ls_formdata-sealcatx  = gs_head-sealcatx.   " 印章种类
  ls_formdata-maktx = gs_head-maktx.          " 品名
  ls_formdata-zitem = ls_jyhead-zitem.         " 项目ID
  ls_formdata-zzitemt = ls_jyhead-zzitemt.      " 项目名称
  ls_formdata-butxt = gs_head-settle_amt.     " 结算金额
  ls_formdata-settle_qty = gs_head-settle_qty." 结算数量
  ls_formdata-settle_unit = gs_head-meins.    " 结算单位
  " 内容摘要
  PERFORM frm_get_txt TABLES lt_suminfo CHANGING lv_suminfo.
  ls_formdata-suminfo = lv_suminfo.


  "结算明细
  LOOP AT gt_item INTO DATA(ls_jsdataitem).
    ls_jsitem-order_no      = ls_jsdataitem-order_no.
    ls_jsitem-order_item    = ls_jsdataitem-order_item.
    ls_jsitem-settle_qty    = ls_jsdataitem-settle_qty.
    ls_jsitem-ord_price     = ls_jsdataitem-ord_price.
    ls_jsitem-oth_price     = ls_jsdataitem-oth_price.
    ls_jsitem-setl_price    = ls_jsdataitem-setl_price.
    ls_jsitem-net_amount    = ls_jsdataitem-net_amount.
    ls_jsitem-tax_amount    = ls_jsdataitem-tax_amount.
    ls_jsitem-total_amount  = ls_jsdataitem-total_amount.
    APPEND ls_jsitem TO lt_jsitem.
    CLEAR ls_jsitem.
  ENDLOOP.
  " 结算明细表
  ls_formdata-zmmt_zsqid_item = lt_jsitem.

  "附件路径
  PERFORM frm_get_fj TABLES lt_filelist CHANGING pv_type pv_msg.
  ls_formdata-filelist = lt_filelist.

  SELECT a~xgsht,
         a~xcdfw,
         b~zzcont
    FROM zmmt_htys_head AS a
    INNER JOIN zmmt_htys_item AS b ON a~zsqid = b~zsqid
    WHERE b~zzcont = @gs_head-zzcont
     INTO TABLE @DATA(lt_ht).

  READ TABLE lt_ht INTO DATA(ls_ht) WITH KEY zzcont = gs_head-zzcont.

*  gs_head-xgsht = ls_ht-xgsht.
*  gs_head = ls_ht-xcdfw.
  "是否为格式合同
  SELECT SINGLE ddtext INTO ls_formdata-xgshtt
    FROM dd07t
   WHERE domname = 'ZD_XGSHT'
     AND ddlanguage = '1'
     AND domvalue_l = ls_ht-xgsht.

  "是否为仓单服务业务下游客户
  SELECT SINGLE ddtext INTO ls_formdata-xcdfwt
    FROM dd07t
   WHERE domname = 'ZD_XCDFW'
     AND ddlanguage = '1'
     AND domvalue_l = ls_ht-xcdfw.




  "交易员（员工号）
  ls_datafield-approveuser = ls_jyhead-zzvkgrp.
  "是否为格式合同
  ls_datafield-xgshtt = ls_formdata-xgshtt.
  "是否为仓单服务业务下游客户
  ls_datafield-xcdfwt = ls_formdata-xcdfwt.

  lv_datafield = /ui2/cl_json=>serialize( data = ls_datafield compress = abap_false
                                    name_mappings = lt_name_mappings
                                    pretty_name = /ui2/cl_json=>pretty_mode-none ).
  ls_bizdata-datafield = lv_datafield.

  lv_formdata = /ui2/cl_json=>serialize( data = ls_formdata compress = abap_false
                                    name_mappings = lt_name_mappings
                                    pretty_name = /ui2/cl_json=>pretty_mode-none ).
  ls_bizdata-formdata = lv_formdata.

  lv_bizdata = /ui2/cl_json=>serialize( data = ls_bizdata compress = abap_false
                                    name_mappings = lt_name_mappings
                                    pretty_name = /ui2/cl_json=>pretty_mode-none ).
  ls_jdata-bizdata    = lv_bizdata.

  pv_json = /ui2/cl_json=>serialize( data = ls_jdata compress = abap_false
                                    name_mappings = lt_name_mappings
                                    pretty_name = /ui2/cl_json=>pretty_mode-none ).

ENDFORM.
*&---------------------------------------------------------------------*
*& Form frm_get_fj
*&---------------------------------------------------------------------*
*& 获取附件
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_get_fj  TABLES   pt_filelist TYPE  zmms_qh1_k2_filelist_req
                  CHANGING pv_type  TYPE bapi_mtype
                           pv_msg   TYPE bapi_msg  .

  DATA: ls_data TYPE zmms_qh1_k2_filelists_req.
  DATA: lv_fileid TYPE string.
  DATA:
    lv_msg    TYPE string,
    lv_subrc  TYPE sy-subrc,
    lv_ufs_id TYPE string.

* 取K2客户端ID
  DATA(lv_clientid_k2) = zdc1_cl_common=>ufs_get_para( zdc1_cl_common=>gcs_atta_param-clientid_k2 ).
  IF lv_clientid_k2 IS INITIAL.
    pv_type = 'E'.
    pv_type = 'K2客户端ID未配置，请通过ZFIM096进行配置'.
  ENDIF.

  SELECT zdjbh,zfjmc,ufs_id FROM zfit_d039_atbase AS t
    INNER JOIN @gt_jsdata_item AS z ON t~zdjbh = z~zsqid
    WHERE zydlx = 'ZMMD033'
    AND   zscbj <> 'X'
    INTO TABLE @DATA(lt_fj).
  IF sy-subrc = 0 .
    LOOP AT lt_fj INTO DATA(ls_fj) .
      ls_data-zzcont = ls_fj-zdjbh.
      ls_data-filename = ls_fj-zfjmc.
      ls_data-fileid   = ls_fj-ufs_id.
      APPEND ls_data TO  pt_filelist.
      lv_fileid = ls_fj-ufs_id.

* 定义文件类型
* 分享文件
      CALL METHOD zdc1_cl_common=>ufs_share
        EXPORTING
          iv_fileid = lv_fileid
          iv_id     = lv_clientid_k2
          iv_type   = 'app'
          iv_appid  = lv_clientid_k2
        IMPORTING
          ev_msg    = lv_msg
        RECEIVING
          rv_subrc  = lv_subrc.
      IF lv_subrc EQ 0.
        pv_type = 'S'.
      ELSE.
        pv_type = 'E'.
        pv_type = lv_msg.
        RETURN.
      ENDIF.
      CLEAR ls_data.
      CLEAR ls_fj.
    ENDLOOP.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Module T_TABLE_GET_LINES OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE t_table_get_lines OUTPUT.
  g_t_table_lines = sy-loopc.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  T_TABLE_MODIFY  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE t_table_modify INPUT.
  DATA lt_ftaxp TYPE TABLE OF ftaxp.
  DATA  lv_mwskz TYPE ekpo-mwskz.


  " 空加新行
  IF gt_item IS INITIAL.
    APPEND INITIAL LINE TO gt_item.
  ENDIF.
  " 本次结算数量必填
  IF gs_item-settle_qty IS INITIAL.
    MESSAGE '本次结算数量必填' TYPE 'E'.
  ENDIF.
  " 订单单价必填
  IF gs_item-ord_price IS INITIAL.
    MESSAGE '订单单价必填' TYPE 'E'.
  ENDIF.
  " 结算单价必填
  IF gs_item-oth_price IS INITIAL AND gs_item-settle_qty IS INITIAL.
    MESSAGE '结算单价必填' TYPE 'E'.
  ENDIF.
  " 货品类型必填
  IF gs_item-zhplx IS INITIAL AND gs_item-oth_price IS NOT INITIAL.
    MESSAGE '货品类型必填' TYPE 'E'.
  ENDIF.
  " 更新方式必填
  "  IF gs_item-update_typ IS INITIAL.
  "    MESSAGE '更新方式必填' TYPE 'E'.
  "  ENDIF.

  "  " 税率计算，由税码转换成税率
  "  IF p_gxfx = 'B'.
  "    SELECT SINGLE
  "           a~mwskz
  "      FROM ekpo AS a
  "      INNER JOIN ekko AS b ON a~ebeln = b~ebeln
  "       WHERE a~ebeln = @gs_item-order_no
  "         AND a~ebelp = @gs_item-order_item
  "       INTO @DATA(lv_mwskz).

  "    IF sy-subrc = 0.
  "      gs_item-mwskz = lv_mwskz.
  "    ENDIF.
  "  ELSEIF p_gxfx = 'S'.
  "    DATA lv_datab TYPE a002-datab.
  "    lv_datab = sy-datum.
  "    SELECT b~mwsk1
  "      FROM a002 AS a
  "      INNER JOIN konp AS b ON a~knumh = b~knumh
  "      WHERE a~aland = 'CN'
  "        AND a~datbi >= @lv_datab
  "        AND a~taxk1 = '1'
  "      INTO @DATA(lv_mwskz1).
  "    ENDSELECT.

  "    IF sy-subrc = 0.
  "      gs_item-mwskz = lv_mwskz1.
  "    ENDIF.
  "  ENDIF.
  "  " 税码
  "  gs_item-mwskz = lv_mwskz.
  "  " 税率
  "  IF gs_item-mwskz IS NOT INITIAL .
  "    CALL FUNCTION 'GET_TAX_PERCENTAGE'
  "      EXPORTING
  "        aland   = 'CN'
  "        datab   = sy-datum
  "        mwskz   = gs_item-mwskz
  "        txjcd   = '1'
  "      TABLES
  "        t_ftaxp = lt_ftaxp.
  "    READ TABLE lt_ftaxp INTO DATA(ls_ftaxp) WITH KEY kschl = 'MWVS'.
  "    IF sy-subrc = 0.
  "      gs_item-tax_rate = ls_ftaxp-kbetr / 10.
  "      CLEAR:ls_ftaxp,lt_ftaxp.
  "    ENDIF.
  "  ENDIF.
  "  " 结算单价
  "  gs_item-setl_price = gs_item-ord_price + gs_item-oth_price.
  "  " 含税金额计算
  "  gs_item-total_amount = gs_item-settle_qty * gs_item-setl_price.
  "  " 不含税金额计算
  "  gs_item-net_amount = gs_item-total_amount / ( 1 + gs_item-tax_rate ).
  "  " 税额计算
  "  gs_item-tax_amount = gs_item-total_amount - gs_item-net_amount.
  " 购销方向为销售 = ‘S’

  " 购销方向为采购 = ‘B’
  IF p_gxfx = 'B'.
    " 税率计算，由税码转换成税率
    SELECT SINGLE
    a~mwskz
    INTO lv_mwskz
    FROM ekpo AS a
    INNER JOIN ekko AS b ON a~ebeln = b~ebeln
    WHERE a~ebeln = gs_item-order_no
    AND a~ebelp = gs_item-order_item.
    .
    IF lv_mwskz IS NOT INITIAL..
      gs_item-mwskz = lv_mwskz.
    ENDIF.

    " 税码
    gs_item-mwskz = lv_mwskz.
    " 税率
    IF gs_item-mwskz IS NOT INITIAL .
      CALL FUNCTION 'GET_TAX_PERCENTAGE'
      EXPORTING
        aland   = 'CN'
        datab   = sy-datum
        mwskz   = gs_item-mwskz
        txjcd   = '1'
      TABLES
        t_ftaxp = lt_ftaxp.
      READ TABLE lt_ftaxp INTO DATA(ls_ftaxp) WITH KEY kschl = 'MWVS'.
      IF sy-subrc = 0.
        gs_item-tax_rate = ls_ftaxp-kbetr / 10.
        CLEAR:ls_ftaxp,lt_ftaxp.
      ENDIF.
    ENDIF.
    " 结算单价
    gs_item-setl_price = gs_item-ord_price + gs_item-oth_price.
    " 含税金额计算
    gs_item-total_amount = gs_item-settle_qty * gs_item-setl_price.
*    " 不含税金额计算
*    gs_item-net_amount = gs_item-total_amount / ( 1 + gs_item-tax_rate ).
*    " 税额计算
*    gs_item-tax_amount = gs_item-total_amount - gs_item-net_amount.
    " 税额计算
    gs_item-tax_amount = gs_item-total_amount / ( 1 + gs_item-tax_rate / 100 ) * gs_item-tax_rate / 100.
    " 不含税金额计算
    gs_item-net_amount = gs_item-total_amount - gs_item-tax_amount.
  ENDIF.

  IF p_gxfx = 'S'.
    " 税率计算，由税码转换成税率
    DATA lv_datab TYPE a002-datab.
    lv_datab = sy-datum.
    SELECT b~mwsk1
      FROM a002 AS a
      INNER JOIN konp AS b ON a~knumh = b~knumh
      WHERE a~aland = 'CN'
        AND a~datbi >= @lv_datab
        AND a~datab <= @lv_datab
        AND a~taxk1 = '1'
      INTO @DATA(lv_mwskz1).
    ENDSELECT.
    " 税码
*    lv_mwskz1 = 'X9'."测试使用
    IF sy-subrc = 0.
      gs_item-mwskz = lv_mwskz1.
    ENDIF.

    " 税率
    IF gs_item-mwskz IS NOT INITIAL .
      CALL FUNCTION 'GET_TAX_PERCENTAGE'
        EXPORTING
          aland   = 'CN'
          datab   = sy-datum
          mwskz   = gs_item-mwskz
          txjcd   = '1'
        TABLES
          t_ftaxp = lt_ftaxp.
      READ TABLE lt_ftaxp INTO DATA(ls_ftaxp1) WITH KEY kschl = 'MWAS'.
      IF sy-subrc = 0.
        gs_item-tax_rate = ls_ftaxp1-kbetr / 10.
        CLEAR:ls_ftaxp1,lt_ftaxp.
      ENDIF.
    ENDIF.
    " 结算单价
    gs_item-setl_price = gs_item-ord_price + gs_item-oth_price.
    " 含税金额计算
    gs_item-total_amount = gs_item-settle_qty * gs_item-setl_price.
*    " 不含税金额计算
*    gs_item-net_amount = gs_item-total_amount / ( 1 + gs_item-tax_rate ).
*    " 税额计算
*    gs_item-tax_amount = gs_item-total_amount - gs_item-net_amount.
    " 税额计算
    gs_item-tax_amount = gs_item-total_amount / ( 1 + gs_item-tax_rate / 100 ) * gs_item-tax_rate / 100.
    " 不含税金额计算
    gs_item-net_amount = gs_item-total_amount - gs_item-tax_amount.
  ENDIF.

  MODIFY gt_item
    FROM gs_item
   INDEX t_table-current_line.

ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  T_TABLE_MARK  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE t_table_mark INPUT.
  DATA: g_t_table_wa2 LIKE LINE OF gt_item.
  IF t_table-line_sel_mode = 1 AND gs_item-box = 'X'.
    LOOP AT gt_item INTO g_t_table_wa2
      WHERE box = 'X'.
      g_t_table_wa2-box = ''.
      MODIFY gt_item
        FROM g_t_table_wa2
        TRANSPORTING box.
    ENDLOOP.
  ENDIF.
  MODIFY gt_item
    FROM gs_item
   INDEX t_table-current_line
  TRANSPORTING box.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MOD_GET_LGORT  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mod_get_lgort INPUT.
  IF gs_item-lgort IS INITIAL.
    CLEAR gs_item-lgobe.
  ELSE.
    "获取库存地点的描述
    SELECT SINGLE
           t001l~lgobe
      INTO gs_item-lgobe
      FROM t001l AS t001l
     INNER JOIN t001w AS t001w
        ON t001w~werks = t001l~werks
     INNER JOIN t001k AS t001k
        ON t001k~bwkey = t001w~bwkey
       AND t001k~bukrs = gs_head-bukrs
     WHERE t001l~lgort = gs_item-lgort.
    IF sy-subrc <> 0.
      CLEAR gs_item-lgobe.
      MESSAGE '库存地点不存在' TYPE 'S' DISPLAY LIKE 'E'.
    ENDIF.
  ENDIF.

  MODIFY gt_item FROM gs_item INDEX t_table-current_line.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MOU_F4_ORDER_NO  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mou_f4_order_no INPUT.

  DATA:BEGIN OF ls_order_no,
         order_no TYPE vbap-vbeln,
       END OF ls_order_no.
  DATA:lt_order_no LIKE TABLE OF ls_order_no.

  SELECT vbeln FROM vbak INTO TABLE @lt_order_no.

  CALL FUNCTION 'F4IF_INT_TABLE_VALUE_REQUEST'
    EXPORTING
      retfield    = 'ORDER_NO'           "表格要显示的字段
      dynpprog    = sy-repid             "返回才程序
      dynpnr      = sy-dynnr             " 屏幕
      dynprofield = 'GS_ITEM-ORDER_NO'   "往页面回填值的地方
      value_org   = 'S'                  "显示类型
    TABLES
      value_tab   = lt_order_no.         "传进去的表格 帮助的内表
  IF sy-subrc <> 0.
    MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
            WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MOU_F4_ORDER_ITEM  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mou_f4_order_item INPUT.

  DATA:BEGIN OF ls_order_item,
         order_no   TYPE vbap-vbeln,
         order_item TYPE vbap-posnr,
       END OF ls_order_item.
  DATA:lt_order_item LIKE TABLE OF ls_order_item.

  SELECT vbeln,posnr FROM vbap
    WHERE vbeln = @gs_item-order_no
    INTO TABLE @lt_order_item.

  CALL FUNCTION 'F4IF_INT_TABLE_VALUE_REQUEST'
    EXPORTING
      retfield    = 'ORDER_ITEM'          "表格要显示的字段
      dynpprog    = sy-repid              "返回才程序
      dynpnr      = sy-dynnr              "屏幕
      dynprofield = 'GS_ITEM-ORDER_ITEM'  "往页面回填值的地方
      value_org   = 'S'                   "显示类型
    TABLES
      value_tab   = lt_order_item.        "传进去的表格 帮助的内表
  IF sy-subrc <> 0.
    MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
            WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MOU_F4_ORDER_QTY  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mou_f4_order_qty INPUT.

  DATA:BEGIN OF ls_order_qty,
         order_no   TYPE vbap-vbeln,
         order_item TYPE vbap-posnr,
         kwmeng     TYPE vbap-kwmeng,
       END OF ls_order_qty.
  DATA:lt_order_qty LIKE TABLE OF ls_order_qty.

  SELECT vbeln,
         posnr,
         kwmeng
    FROM vbap
   WHERE vbeln = @gs_item-order_no
     AND posnr = @gs_item-order_item
    INTO TABLE @lt_order_qty.

  CALL FUNCTION 'F4IF_INT_TABLE_VALUE_REQUEST'
    EXPORTING
      retfield    = 'ORDER_QTY'                "表格要显示的字段
      dynpprog    = sy-repid                   "返回才程序
      dynpnr      = sy-dynnr                   "屏幕
      dynprofield = 'GS_ITEM-ORDER_QTY'        "往页面回填值的地方
      value_org   = 'S'                        "显示类型
    TABLES
      value_tab   = lt_order_qty.              "传进去的表格 帮助的内表
  IF sy-subrc <> 0.
    MESSAGE ID sy-msgid TYPE sy-msgty NUMBER sy-msgno
            WITH sy-msgv1 sy-msgv2 sy-msgv3 sy-msgv4.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MOU_F4_ZZGXDQ  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mou_f4_zzgxdq INPUT.
  DATA: lt_returndq TYPE TABLE OF ddshretval.

  DATA:BEGIN OF ls_zzgxdq,
         werks       TYPE zmmt_gxdq_mat-werks,
         matnr       TYPE zmmt_gxdq_mat-matnr,
         zzgxdq      TYPE zmmt_gxdq_mat-zzgxdq,
         zzgxdq_name TYPE zmmt_gxdq-zzgxdq_name,
       END OF ls_zzgxdq.
  DATA:lt_zzgxdq LIKE TABLE OF ls_zzgxdq.

  SELECT SINGLE
         a~matnr,
         a~vbeln,
         a~posnr
    FROM vbap AS a
    INNER JOIN vbak AS b ON a~vbeln = b~vbeln
    WHERE a~vbeln = @gs_item-order_no
      AND a~posnr = @gs_item-order_item
    INTO @DATA(ls_wl).

  SELECT a~werks,
         a~matnr,
         a~zzgxdq,
         b~zzgxdq_name
    FROM zmmt_gxdq_mat AS a
    INNER JOIN zmmt_gxdq AS b ON a~zzgxdq = b~zzgxdq
    WHERE a~werks = '3001'        "@gs_item-werks
      AND a~matnr = @ls_wl-matnr
    INTO TABLE @lt_zzgxdq.

  CALL FUNCTION 'F4IF_INT_TABLE_VALUE_REQUEST'
    EXPORTING
      retfield        = 'ZZGXDQ'            "表格要显示的字段
      dynpprog        = sy-repid            "返回才程序
      dynpnr          = sy-dynnr            "屏幕
*     dynprofield     = 'GS_ITEM-ZZGXDQ'    "往页面回填值的地方
      value_org       = 'S'                 "显示类型
      multiple_choice = 'X'
      display         = ''
    TABLES
      value_tab       = lt_zzgxdq               "传进去的表格 帮助的内表
      return_tab      = lt_returndq.
  IF sy-subrc = 0.
    CLEAR gs_item-zzgxdq.
    LOOP AT lt_returndq INTO DATA(ls_returndq).
      IF gs_item-zzgxdq IS INITIAL.
        gs_item-zzgxdq = ls_returndq-fieldval.
      ELSE.
        gs_item-zzgxdq = gs_item-zzgxdq && '/' && ls_returndq-fieldval.
      ENDIF.
    ENDLOOP.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*& Form FRM_GET_TXT
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*&      --> LT_SUMINFO
*&      <-- LV_SUMINFO
*&---------------------------------------------------------------------*
FORM frm_get_txt  TABLES   pt_suminfo TYPE  zmms_qh1_k2_suminfo_req
                   CHANGING pv_txt TYPE string.

  DATA: lv_name TYPE thead-tdname.
  DATA: ls_suminfo LIKE LINE OF pt_suminfo.
  DATA: lt_lines    TYPE STANDARD TABLE OF tline,
        lt_line     TYPE STANDARD TABLE OF tline,
        lt_lines_ot TYPE TABLE OF line.
  DATA: ls_tdline TYPE string.
  " 获取结算申请长文本
  lv_name = gs_head-zsqid.
  CALL FUNCTION 'READ_TEXT'
    EXPORTING
      client                  = sy-mandt
      id                      = 'LTXT'
      language                = sy-langu
      name                    = lv_name
      object                  = 'ZMM_JSSQ'
    TABLES
      lines                   = lt_lines
    EXCEPTIONS
      id                      = 1
      language                = 2
      name                    = 3
      not_found               = 4
      object                  = 5
      reference_check         = 6
      wrong_access_to_archive = 7
      OTHERS                  = 8.
  IF sy-subrc = 0 .
    LOOP AT lt_lines INTO DATA(ls_line).

      CLEAR: lt_line,lt_lines_ot,ls_tdline.
      APPEND ls_line TO lt_line.
      CALL FUNCTION 'CONVERT_ITF_TO_STREAM_TEXT'
        EXPORTING
          language    = sy-langu
        TABLES
          itf_text    = lt_line
          text_stream = lt_lines_ot.
      LOOP AT lt_lines_ot INTO DATA(ls_ot).
        ls_tdline = ls_tdline && ls_ot-line.
      ENDLOOP.

      IF pv_txt IS INITIAL.
        pv_txt = ls_tdline.
      ELSE.
        CONCATENATE pv_txt cl_abap_char_utilities=>newline ls_tdline INTO pv_txt.
      ENDIF.
    ENDLOOP.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Form FRM_CANCEL_JS
*&---------------------------------------------------------------------*
*& text
*&---------------------------------------------------------------------*
*& -->  p1        text
*& <--  p2        text
*&---------------------------------------------------------------------*
FORM frm_cancel_js .
  DATA: ls_zmmt_settle_head TYPE zmmt_settle_head.
  DATA: lt_zmmt_jyys_head TYPE TABLE OF zmmt_jyys_head.
  DATA: lv_flag TYPE string.

  CALL FUNCTION 'POPUP_TO_CONFIRM'
    EXPORTING
      titlebar              = '作废结算申请'
      text_question         = TEXT-004
      display_cancel_button = ''
      popup_type            = 'ICON_MESSAGE_WARNING'
    IMPORTING
      answer                = lv_flag
    EXCEPTIONS
      text_not_found        = 1
      OTHERS                = 2.

  IF sy-subrc = 0.
    IF lv_flag = '1'.
      gs_head-zunam = sy-uname.
      gs_head-zudat = sy-datum.
      gs_head-zutim = sy-uzeit.

      " 更新结算申请抬头表
      UPDATE zmmt_settle_head SET zstatus = '05' zunam = sy-uname zudat = sy-datum zutim = sy-uzeit
      WHERE zsqid = gs_head-zsqid.

      IF sy-subrc = 0.
        COMMIT WORK AND WAIT.
        MESSAGE '结算申请作废成功' TYPE 'E' DISPLAY LIKE 'S'.
      ENDIF.
    ENDIF.
  ENDIF.

ENDFORM.
*&---------------------------------------------------------------------*
*& Module M_REFRESH OUTPUT
*&---------------------------------------------------------------------*
*&
*&---------------------------------------------------------------------*
MODULE m_refresh OUTPUT.
  LOOP AT gt_item INTO DATA(ls_item).
    gs_head-settle_qty = ls_item-settle_qty.
    gs_head-settle_amt = ls_item-total_amount.
  ENDLOOP.
ENDMODULE.

MODULE set_col OUTPUT.
  DATA: ls_col LIKE LINE OF t_table-cols.
  LOOP AT t_table-cols INTO ls_col WHERE screen-name CS 'GS_ITEM-BOX' .
    ls_col-vislength = 0.
    MODIFY t_table-cols FROM ls_col.
  ENDLOOP.
  " 当未勾选【按批次结算】，购销方向为B（采购）
  IF p_pcjs IS INITIAL AND p_gxfx = 'B'.
    "    LOOP AT SCREEN.
    "      IF screen-name CS 'GS_ITEM-WERKS' OR "工厂
    "         screen-name CS 'GS_ITEM-LGORT' OR " 库存地点
    "         screen-name CS 'GS_ITEM-LGOBE' OR " 仓库名称
    "         screen-name CS 'GS_ITEM-VBELN' OR " 交货单
    "         screen-name CS 'GS_ITEM-POSNR' OR " 交货项目
    "         screen-name CS 'GS_ITEM-Z_BATCH_011' OR " 到货批次
    "         screen-name CS 'GS_ITEM-CLABS'.    " 库存数量
    "        " 不可见
    "        screen-active = 0.
    "      ENDIF.
    "      MODIFY SCREEN.
    "    ENDLOOP.
    LOOP AT t_table-cols INTO ls_col WHERE screen-name CS 'GS_ITEM-WERKS' OR "工厂
         screen-name CS 'GS_ITEM-LGORT' OR " 库存地点
         screen-name CS 'GS_ITEM-LGOBE' OR " 仓库名称
         screen-name CS 'GS_ITEM-VBELN' OR " 交货单
         screen-name CS 'GS_ITEM-POSNR' OR " 交货项目
         screen-name CS 'GS_ITEM-Z_BATCH_011' OR " 到货批次
         screen-name CS 'GS_ITEM-CLABS'.    " 库存数量
      ls_col-vislength = 0.
      MODIFY t_table-cols FROM ls_col.
    ENDLOOP.
  ENDIF.

  " 当勾选【按批次结算】，购销方向为B（采购）
  IF p_pcjs IS NOT INITIAL AND p_gxfx = 'B'.
    "    LOOP AT SCREEN.
    "      IF screen-name CS 'GS_ITEM-WERKS' OR "工厂
    "         screen-name CS 'GS_ITEM-LGORT' OR " 库存地点
    "         screen-name CS 'GS_ITEM-LGOBE' OR " 仓库名称
    "         screen-name CS 'GS_ITEM-CLABS'.   " 库存数量
    "        " 不可见
    "        screen-active = 0.
    "      ENDIF.
    "      MODIFY SCREEN.
    "    ENDLOOP.

    LOOP AT t_table-cols INTO ls_col WHERE screen-name CS 'GS_ITEM-WERKS' OR "工厂
         screen-name CS 'GS_ITEM-LGORT' OR " 库存地点
         screen-name CS 'GS_ITEM-LGOBE' OR " 仓库名称
         screen-name CS 'GS_ITEM-CLABS'.   " 库存数量
      ls_col-vislength = 0.
      MODIFY t_table-cols FROM ls_col.
    ENDLOOP.
  ENDIF.


  " 当未勾选【按批次结算】，购销方向为S（销售）
  IF p_pcjs IS INITIAL AND p_gxfx = 'S'.
    "    LOOP AT SCREEN.
    "      IF screen-name CS 'GS_ITEM-WERKS' OR "工厂
    "         screen-name CS 'GS_ITEM-LGORT' OR " 库存地点
    "         screen-name CS 'GS_ITEM-LGOBE' OR " 仓库名称
    "         screen-name CS 'GS_ITEM-VBELN' OR " 交货单
    "         screen-name CS 'GS_ITEM-POSNR' OR " 交货项目
    "         screen-name CS 'GS_ITEM-Z_BATCH_011' OR " 到货批次
    "         screen-name CS 'GS_ITEM-CLABS'.    " 库存数量
    "        " 不可见
    "        screen-active = 0.
    "      ENDIF.

    "      MODIFY SCREEN.
    "    ENDLOOP.

    LOOP AT t_table-cols INTO ls_col WHERE screen-name = 'GS_ITEM-WERKS'OR "工厂
                                           screen-name CS 'GS_ITEM-LGORT' OR " 库存地点
                                           screen-name CS 'GS_ITEM-LGOBE' OR " 仓库名称
                                           screen-name CS 'GS_ITEM-VBELN' OR " 交货单
                                           screen-name CS 'GS_ITEM-POSNR' OR " 交货项目
                                           screen-name CS 'GS_ITEM-Z_BATCH_011' OR " 到货批次
                                           screen-name CS 'GS_ITEM-CLABS'.    " 库存数量
      ls_col-vislength = 0.
      MODIFY t_table-cols FROM ls_col.
    ENDLOOP.

  ENDIF.

  " 当勾选【按批次结算】，购销方向为S（销售）
  IF p_pcjs IS NOT INITIAL AND p_gxfx = 'S'.
    "    LOOP AT SCREEN.
    "      IF screen-name CS 'GS_ITEM-VBELN' OR " 交货单
    "         screen-name CS 'GS_ITEM-POSNR' OR " 交货项目
    "         screen-name CS 'GS_ITEM-SQTY1'.   " 已结算数量
    "        " 不可见
    "        screen-active = 0.
    "      ENDIF.
    "      MODIFY SCREEN.
    "    ENDLOOP.

    LOOP AT t_table-cols INTO ls_col WHERE screen-name CS 'GS_ITEM-VBELN' OR " 交货单
     screen-name CS 'GS_ITEM-POSNR' OR " 交货项目
     screen-name CS 'GS_ITEM-SQTY1'.   " 已结算数量
      ls_col-vislength = 0.
      MODIFY t_table-cols FROM ls_col.
    ENDLOOP.
  ENDIF.

  " 当勾选【按批次结算】，购销方向为S（销售），结算类型 = '2'（二次结算）
  IF p_pcjs IS NOT INITIAL AND p_gxfx = 'S' AND p_stltyp = '2'.
    "    LOOP AT SCREEN.
    "      IF screen-name CS 'GS_ITEM-WERKS' OR "工厂
    "         screen-name CS 'GS_ITEM-LGORT' OR " 库存地点
    "         screen-name CS 'GS_ITEM-LGOBE' OR " 仓库名称
    "         screen-name CS 'GS_ITEM-CLABS' OR " 库存数量
    "         screen-name CS 'GS_ITEM-MENGE'.   " 订单数量
    "        " 不可见
    "        screen-active = 0.
    "      ENDIF.
    "      MODIFY SCREEN.
    "    ENDLOOP.
    LOOP AT t_table-cols INTO ls_col WHERE screen-name CS 'GS_ITEM-WERKS' OR "工厂
     screen-name CS 'GS_ITEM-LGORT' OR " 库存地点
     screen-name CS 'GS_ITEM-LGOBE' OR " 仓库名称
     screen-name CS 'GS_ITEM-CLABS' OR " 库存数量
     screen-name CS 'GS_ITEM-MENGE'.   " 订单数量
      ls_col-vislength = 0.
      MODIFY t_table-cols FROM ls_col.
    ENDLOOP.
  ENDIF.
ENDMODULE.
*&---------------------------------------------------------------------*
*&      Module  MOD_F4_SEALCATX  INPUT
*&---------------------------------------------------------------------*
*       text
*----------------------------------------------------------------------*
MODULE mod_f4_sealcatx INPUT.
  DATA: lt_return TYPE TABLE OF ddshretval.

  SELECT domvalue_l,ddtext FROM dd07t WHERE ddlanguage = '1'
    AND domname = 'ZD_SEALCATX' INTO TABLE @DATA(lt_dd07t).
  SORT lt_dd07t BY domvalue_l.

  CALL FUNCTION 'F4IF_INT_TABLE_VALUE_REQUEST'
    EXPORTING
      retfield        = 'SEALCATX'
      dynpprog        = sy-repid
      dynpnr          = sy-dynnr
      value_org       = 'S'
      multiple_choice = 'X'
      display         = ''
*     CALLBACK_PROGRAM = SY-REPID
**     CALLBACK_FORM    = 'FRM_CB_FORM'
* IMPORTING
*     USER_RESET      =
    TABLES
      value_tab       = lt_dd07t
*     FIELD_TAB       =
      return_tab      = lt_return
*     DYNPFLD_MAPPING =
    EXCEPTIONS
      parameter_error = 1
      no_values_found = 2
      OTHERS          = 3.
  IF sy-subrc = 0.
    CLEAR gs_head-sealcatx.
    LOOP AT lt_return INTO DATA(ls_return).
      IF gs_head-sealcatx IS INITIAL.
        gs_head-sealcatx = ls_return-fieldval.
      ELSE.
        gs_head-sealcatx = gs_head-sealcatx && '/' && ls_return-fieldval.
      ENDIF.
    ENDLOOP.
  ENDIF.
ENDMODULE.