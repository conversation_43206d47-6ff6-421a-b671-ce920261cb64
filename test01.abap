REPORT zflight_info_salv.

TABLES: spfli.

DATA: gt_spfli TYPE TABLE OF spfli,
      go_alv   TYPE REF TO cl_salv_table.

" 增加航班号作为选择屏幕输入条件
SELECTION-SCREEN: BEGIN OF BLOCK b1 WITH FRAME TITLE TEXT-001.
PARAMETERS: p_carrid TYPE spfli-carrid OBLIGATORY.
SELECTION-SCREEN: END OF BLOCK b1.

" 增加保存按钮
SELECTION-SCREEN: PUSHBUTTON /10(10) p_save USER-COMMAND p_save_button.

START-OF-SELECTION.

  " 读取SPFLI表数据，增加过滤条件
  SELECT * FROM spfli INTO TABLE gt_spfli WHERE carrid = p_carrid.

  " 检查数据是否读取成功
  IF gt_spfli IS INITIAL.
    MESSAGE 'No data found in SPFLI table.' TYPE 'I'.
  ELSE.
    " 创建ALV对象
    TRY.
        cl_salv_table=>factory(
          IMPORTING
            r_salv_table = go_alv
          CHANGING
            t_table      = gt_spfli ).
      CATCH cx_salv_msg INTO DATA(lo_msg).
        MESSAGE lo_msg->get_text( ) TYPE 'E'.
    ENDTRY.

    " 设置允许修改非主键字段
    go_alv->get_columns( )->set_optimize( abap_true ).
    go_alv->get_columns( )->get_column( 'CITYFROM' )->set_edit( abap_true ).
    go_alv->get_columns( )->get_column( 'CITYTO' )->set_edit( abap_true ).
    go_alv->get_columns( )->get_column( 'DISTANCE' )->set_edit( abap_true ).

    " 显示ALV报表
    go_alv->display( ).
  ENDIF.

" 处理保存按钮点击事件
MODULE user_command_0100 INPUT.
  CASE sy-ucomm.
    WHEN 'P_SAVE_BUTTON'.
      PERFORM update_spfli_table.
  ENDCASE.
ENDMODULE.

FORM update_spfli_table.
  DATA: lt_modified_data TYPE TABLE OF spfli,
        ls_modified_data TYPE spfli.

  " 获取修改后的数据
  go_alv->get_table( IMPORTING et_table = lt_modified_data ).

  " 更新SPFLI表
  LOOP AT lt_modified_data INTO ls_modified_data.
    UPDATE spfli SET cityfrom = ls_modified_data-cityfrom
                    cityto   = ls_modified_data-cityto
                    distance = ls_modified_data-distance
          WHERE carrid = ls_modified_data-carrid
            AND connid = ls_modified_data-connid.
    IF sy-subrc <> 0.
      WRITE: / 'Update failed for:', ls_modified_data.
    ELSE.
      WRITE: / 'Update successful for:', ls_modified_data.
    ENDIF.
  ENDLOOP.
ENDFORM.