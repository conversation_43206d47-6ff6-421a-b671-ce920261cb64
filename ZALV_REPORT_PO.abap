REPORT zalv_report_po.

TABLES: ekko, ekpo, lfa1, mara.

TYPES: BEGIN OF zalv_po_output,
  ebeln TYPE ekko-ebeln,  "采购订单
  bukrs TYPE ekko-bukrs,  "公司代码
  lifnr TYPE ekko-lifnr,  "供应商
  name1 TYPE lfa1-name1,  "供应商名称
  ebelp TYPE ekpo-ebelp,  "行项目
  matnr TYPE ekpo-matnr,  "物料
  maktx TYPE makt-maktx,  "物料描述
END OF zalv_po_output.

DATA: gt_output TYPE TABLE OF zalv_po_output,
      gs_output TYPE zalv_po_output.

DATA: gt_fieldcat TYPE lvc_t_fcat,
      gs_fieldcat TYPE lvc_s_fcat.

SELECTION-SCREEN BEGIN OF BLOCK b1 WITH FRAME TITLE TEXT-001.
PARAMETERS: p_bukrs TYPE ekko-bukrs OBLIGATORY,
            p_lifnr TYPE ekko-lifnr,
            p_matnr TYPE ekpo-matnr.
SELECTION-SCREEN END OF BLOCK b1.

START-OF-SELECTION.

  PERFORM get_data.
  PERFORM build_fieldcat.
  PERFORM display_alv.

FORM get_data.

  SELECT ekko~ebeln ekko~bukrs ekko~lifnr lfa1~name1
         ekpo~ebelp ekpo~matnr mara~maktx
    INTO CORRESPONDING FIELDS OF TABLE gt_output
    FROM ekko
    INNER JOIN ekpo ON ekko~ebeln = ekpo~ebeln
    LEFT JOIN lfa1 ON ekko~lifnr = lfa1~lifnr
    LEFT JOIN mara ON ekpo~matnr = mara~matnr
    WHERE ekko~bukrs = p_bukrs
      AND ekko~lifnr = p_lifnr
      AND ekpo~matnr = p_matnr.

ENDFORM.

FORM build_fieldcat.

  PERFORM add_field USING:
    'EBELN' '采购订单' 10,
    'BUKRS' '公司代码' 4,
    'LIFNR' '供应商' 10,
    'NAME1' '供应商名称' 35,
    'EBELP' '行项目' 5,
    'MATNR' '物料' 18,
    'MAKTX' '物料描述' 40.

ENDFORM.

FORM add_field USING p_fieldname p_coltext p_outputlen.
  CLEAR gs_fieldcat.
  gs_fieldcat-fieldname = p_fieldname.
  gs_fieldcat-coltext   = p_coltext.
  gs_fieldcat-outputlen = p_outputlen.
  APPEND gs_fieldcat TO gt_fieldcat.
ENDFORM.

FORM display_alv.

  DATA: lo_alv TYPE REF TO cl_salv_table.

  TRY.
      cl_salv_table=>factory(
        IMPORTING
          r_salv_table = lo_alv
        CHANGING
          t_table      = gt_output ).

      lo_alv->get_columns( )->set_optimize( abap_true ).
      lo_alv->get_columns( )->set_cell_type_column( 'CELLTYPE' ).

      lo_alv->display( ).

    CATCH cx_salv_msg INTO DATA(lx_salv_msg).
      MESSAGE lx_salv_msg TYPE 'I' DISPLAY LIKE 'E'.
  ENDTRY.

ENDFORM.
